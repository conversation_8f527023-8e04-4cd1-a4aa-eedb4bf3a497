DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=127.0.0.1, gov.nist.javax.sip.DEBUG_LOG=siplog.txt}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="127.0.0.1" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@32d48f86 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@9936354]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@9936354]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@4f9a8d71]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:<EMAIL> SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = sip.netgsm.com.tr:5060/UDP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(05f6ccfc-5643-4573-9567-b6d2870286f4:dec0efbf-975b-4f18-99e6-2133b521ba62) : returning null]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ************/5060]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][SipClientService.java:112][NetGsmCallService.java:191][AiCallCenterController.java:298][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKac38b22f31ad0cf42c9879ba25923293]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bkac38b22f31ad0cf42c9879ba25923293]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKac38b22f31ad0cf42c9879ba25923293]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:<EMAIL>>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@5e09f64ddec0efbf-975b-4f18-99e6-2133b521ba62/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bkac38b22f31ad0cf42c9879ba25923293/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][SipClientService.java:112][NetGsmCallService.java:191][AiCallCenterController.java:298][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@5e09f64d]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 5061]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][SipClientService.java:112][NetGsmCallService.java:191][AiCallCenterController.java:298][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: 05f6ccfc-5643-4573-9567-b6d2870286f4:dec0efbf-975b-4f18-99e6-2133b521ba62sipDialog = gov.nist.javax.sip.stack.SIPDialog@5e09f64d]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:<EMAIL> SIP/2.0
Call-ID: 05f6ccfc-5643-4573-9567-b6d2870286f4
CSeq: 1 INVITE
From: <sip:<EMAIL>>;tag=dec0efbf-975b-4f18-99e6-2133b521ba62
To: <sip:<EMAIL>>
Via: SIP/2.0/UDP 127.0.0.1:5061;branch=z9hG4bKac38b22f31ad0cf42c9879ba25923293
Max-Forwards: 70
Contact: <sip:2129091697@127.0.0.1:5061>
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:<EMAIL> SIP/2.0
Call-ID: 05f6ccfc-5643-4573-9567-b6d2870286f4
CSeq: 1 INVITE
From: <sip:<EMAIL>>;tag=dec0efbf-975b-4f18-99e6-2133b521ba62
To: <sip:<EMAIL>>
Via: SIP/2.0/UDP 127.0.0.1:5061;branch=z9hG4bKac38b22f31ad0cf42c9879ba25923293
Max-Forwards: 70
Contact: <sip:2129091697@127.0.0.1:5061>
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKac38b22f31ad0cf42c9879ba25923293]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@bfe99252 branchID = z9hG4bKac38b22f31ad0cf42c9879ba25923293 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][SipClientService.java:113][NetGsmCallService.java:191][AiCallCenterController.java:298][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@bfe99252 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:654][MessageChannel.java:235][SIPTransaction.java:738][SIPClientTransaction.java:485][SIPClientTransaction.java:983][SipClientService.java:113][NetGsmCallService.java:191][AiCallCenterController.java:298][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:663) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ************/5060
INVITE sip:<EMAIL> SIP/2.0
Call-ID: 05f6ccfc-5643-4573-9567-b6d2870286f4
CSeq: 1 INVITE
From: <sip:<EMAIL>>;tag=dec0efbf-975b-4f18-99e6-2133b521ba62
To: <sip:<EMAIL>>
Via: SIP/2.0/UDP 127.0.0.1:5061;branch=z9hG4bKac38b22f31ad0cf42c9879ba25923293
Max-Forwards: 70
Contact: <sip:2129091697@127.0.0.1:5061>
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:666) [*******************
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@bfe99252 branchID = z9hG4bKac38b22f31ad0cf42c9879ba25923293 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][SipClientService.java:113][NetGsmCallService.java:191][AiCallCenterController.java:298][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@bfe99252 branchID = z9hG4bKac38b22f31ad0cf42c9879ba25923293 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][SipClientService.java:113][NetGsmCallService.java:191][AiCallCenterController.java:298][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@bfe99252 isReliable false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bkac38b22f31ad0cf42c9879ba25923293 transaction = gov.nist.javax.sip.stack.SIPClientTransaction@bfe99252]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@bfe99252 KEY = z9hg4bkac38b22f31ad0cf42c9879ba25923293]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@46add243]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@46add243]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:572][Timer.java:522]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@63fcd284]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@63fcd284nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@46add243]source = gov.nist.javax.sip.SipProviderImpl@46add243]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@bfe99252]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:<EMAIL> SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = sip.netgsm.com.tr:5060/UDP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(c584cf10-0bb6-4343-9cad-0b172eddad38:7e7bd178-42fe-4b34-a1bc-5060eec71cd1) : returning null]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ************/5060]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][SipClientService.java:112][NetGsmCallService.java:191][AiCallCenterController.java:83][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKf075dd9d331f7c51127cad64f534e308]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bkf075dd9d331f7c51127cad64f534e308]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKf075dd9d331f7c51127cad64f534e308]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:<EMAIL>>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@44e1eda07e7bd178-42fe-4b34-a1bc-5060eec71cd1/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bkf075dd9d331f7c51127cad64f534e308/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][SipClientService.java:112][NetGsmCallService.java:191][AiCallCenterController.java:83][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@44e1eda0]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 5061]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][SipClientService.java:112][NetGsmCallService.java:191][AiCallCenterController.java:83][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: c584cf10-0bb6-4343-9cad-0b172eddad38:7e7bd178-42fe-4b34-a1bc-5060eec71cd1sipDialog = gov.nist.javax.sip.stack.SIPDialog@44e1eda0]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:<EMAIL> SIP/2.0
Call-ID: c584cf10-0bb6-4343-9cad-0b172eddad38
CSeq: 1 INVITE
From: <sip:<EMAIL>>;tag=7e7bd178-42fe-4b34-a1bc-5060eec71cd1
To: <sip:<EMAIL>>
Via: SIP/2.0/UDP 127.0.0.1:5061;branch=z9hG4bKf075dd9d331f7c51127cad64f534e308
Max-Forwards: 70
Contact: <sip:2129091697@127.0.0.1:5061>
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:<EMAIL> SIP/2.0
Call-ID: c584cf10-0bb6-4343-9cad-0b172eddad38
CSeq: 1 INVITE
From: <sip:<EMAIL>>;tag=7e7bd178-42fe-4b34-a1bc-5060eec71cd1
To: <sip:<EMAIL>>
Via: SIP/2.0/UDP 127.0.0.1:5061;branch=z9hG4bKf075dd9d331f7c51127cad64f534e308
Max-Forwards: 70
Contact: <sip:2129091697@127.0.0.1:5061>
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKf075dd9d331f7c51127cad64f534e308]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@959b81c3 branchID = z9hG4bKf075dd9d331f7c51127cad64f534e308 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][SipClientService.java:113][NetGsmCallService.java:191][AiCallCenterController.java:83][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@959b81c3 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:654][MessageChannel.java:235][SIPTransaction.java:738][SIPClientTransaction.java:485][SIPClientTransaction.java:983][SipClientService.java:113][NetGsmCallService.java:191][AiCallCenterController.java:83][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:663) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ************/5060
INVITE sip:<EMAIL> SIP/2.0
Call-ID: c584cf10-0bb6-4343-9cad-0b172eddad38
CSeq: 1 INVITE
From: <sip:<EMAIL>>;tag=7e7bd178-42fe-4b34-a1bc-5060eec71cd1
To: <sip:<EMAIL>>
Via: SIP/2.0/UDP 127.0.0.1:5061;branch=z9hG4bKf075dd9d331f7c51127cad64f534e308
Max-Forwards: 70
Contact: <sip:2129091697@127.0.0.1:5061>
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:666) [*******************
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@959b81c3 branchID = z9hG4bKf075dd9d331f7c51127cad64f534e308 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][SipClientService.java:113][NetGsmCallService.java:191][AiCallCenterController.java:83][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@959b81c3 branchID = z9hG4bKf075dd9d331f7c51127cad64f534e308 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][SipClientService.java:113][NetGsmCallService.java:191][AiCallCenterController.java:83][DirectMethodHandleAccessor.java:103][Method.java:580][InvocableHandlerMethod.java:258][InvocableHandlerMethod.java:191][ServletInvocableHandlerMethod.java:118][RequestMappingHandlerAdapter.java:986][RequestMappingHandlerAdapter.java:891][AbstractHandlerMethodAdapter.java:87][DispatcherServlet.java:1089][DispatcherServlet.java:979][FrameworkServlet.java:1014][FrameworkServlet.java:914][HttpServlet.java:590][FrameworkServlet.java:885][HttpServlet.java:658][ApplicationFilterChain.java:195][ApplicationFilterChain.java:140][WsFilter.java:51][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][RequestContextFilter.java:100][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][FormContentFilter.java:93][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][ServerHttpObservationFilter.java:114][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][CharacterEncodingFilter.java:201][OncePerRequestFilter.java:116][ApplicationFilterChain.java:164][ApplicationFilterChain.java:140][StandardWrapperValve.java:167][StandardContextValve.java:90][AuthenticatorBase.java:483][StandardHostValve.java:116][ErrorReportValve.java:93][StandardEngineValve.java:74][CoyoteAdapter.java:344][Http11Processor.java:398][AbstractProcessorLight.java:63][AbstractProtocol.java:903][NioEndpoint.java:1740][SocketProcessorBase.java:52][ThreadPoolExecutor.java:1189][ThreadPoolExecutor.java:658][TaskThread.java:63][Thread.java:1575]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@959b81c3 isReliable false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bkf075dd9d331f7c51127cad64f534e308 transaction = gov.nist.javax.sip.stack.SIPClientTransaction@959b81c3]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@959b81c3 KEY = z9hg4bkf075dd9d331f7c51127cad64f534e308]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@46add243]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@46add243]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:572][Timer.java:522]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@7efd09ff]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@7efd09ffnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@46add243]source = gov.nist.javax.sip.SipProviderImpl@46add243]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@959b81c3]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@46b6ce71 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@262d6ef0]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@262d6ef0]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@1b9785aa]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@4498e76a listeningPoint = gov.nist.javax.sip.ListeningPointImpl@4605b20b]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@4605b20b]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@210e971e]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@41404e50 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@16535150]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@16535150]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@1b9785aa]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@754345b listeningPoint = gov.nist.javax.sip.ListeningPointImpl@75491241]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@75491241]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@71d3dfd5]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@75304b3b listeningPoint = gov.nist.javax.sip.ListeningPointImpl@672b0f0a]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@672b0f0a]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@15311af2]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@f9b0bfb listeningPoint = gov.nist.javax.sip.ListeningPointImpl@653d1d7c]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@653d1d7c]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@ba4c99c]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@2eaad7c0 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@2d0ac6c8]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@2d0ac6c8]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@1203c259]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@2b381c76 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@557665b2]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@557665b2]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@340f4723]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@14b784c0 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@2fdcba46]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@2fdcba46]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@6dabad5c]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@74ba0d7 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@74f0bfba]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@74f0bfba]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@21b508a0]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@66f9c01b listeningPoint = gov.nist.javax.sip.ListeningPointImpl@61a7c3a7]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@61a7c3a7]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@4fd2b418]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@78546c38 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@3479ffd1]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@3479ffd1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@5e79bdf6]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@6d9a8a31 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@346fa3bf]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@346fa3bf]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@f1b0658]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@66403e70 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@67adede9]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@67adede9]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@6c8b6782]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@abbe7b3 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@179695c7]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@179695c7]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@54602d5a]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@1a3a1b59 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@5f2066c6]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@5f2066c6]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@4b391e45]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS=false, gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=AI_Call_Center_SIP, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=siplog.txt, gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS=false, gov.nist.javax.sip.MAX_MESSAGE_SIZE=1048576}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="AI_Call_Center_SIP"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 127.0.0.1 port = 5061 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@68caf7f4 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@22832f3c]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@22832f3c]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.backend360.ai_call_center.service.SipClientService@56929ce]

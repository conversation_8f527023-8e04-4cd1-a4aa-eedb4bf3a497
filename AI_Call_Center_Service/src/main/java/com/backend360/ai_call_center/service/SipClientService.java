package com.backend360.ai_call_center.service;

import com.backend360.ai_call_center.config.NetGsmSipConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sip.*;
import javax.sip.address.Address;
import javax.sip.address.AddressFactory;
import javax.sip.address.SipURI;
import javax.sip.header.*;
import javax.sip.message.MessageFactory;
import javax.sip.message.Request;
import javax.sip.message.Response;
import java.text.ParseException;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class SipClientService implements SipListener {

    private final NetGsmSipConfig sipConfig;
    
    private SipStack sipStack;
    private SipProvider sipProvider;
    private AddressFactory addressFactory;
    private HeaderFactory headerFactory;
    private MessageFactory messageFactory;
    private ListeningPoint listeningPoint;
    
    private boolean isInitialized = false;
    private final Map<String, CallSession> activeCalls = new HashMap<>();

    /**
     * SIP Stack'i başlatır
     */
    public void initializeSipStack() {
        if (isInitialized) {
            log.info("SIP Stack zaten başlatılmış");
            return;
        }
        
        try {
            log.info("SIP Stack başlatılıyor...");
            
            // SIP Stack Properties - Network düzeltmeleri
            Properties properties = new Properties();
            properties.setProperty("javax.sip.STACK_NAME", "AI_Call_Center_SIP");
            properties.setProperty("javax.sip.IP_ADDRESS", "0.0.0.0"); // Tüm interface'leri dinle
            properties.setProperty("gov.nist.javax.sip.TRACE_LEVEL", "32");
            properties.setProperty("gov.nist.javax.sip.DEBUG_LOG", "siplog.txt");
            properties.setProperty("gov.nist.javax.sip.SERVER_LOG", "sipserver.log");

            // Network ayarları
            properties.setProperty("gov.nist.javax.sip.MAX_MESSAGE_SIZE", "1048576");
            properties.setProperty("gov.nist.javax.sip.CACHE_CLIENT_CONNECTIONS", "false");
            properties.setProperty("gov.nist.javax.sip.USE_ROUTER_FOR_ALL_URIS", "false");
            
            // SIP Stack oluştur
            SipFactory sipFactory = SipFactory.getInstance();
            sipFactory.setPathName("gov.nist");
            sipStack = sipFactory.createSipStack(properties);
            
            // Factories oluştur
            headerFactory = sipFactory.createHeaderFactory();
            addressFactory = sipFactory.createAddressFactory();
            messageFactory = sipFactory.createMessageFactory();
            
            // Listening Point oluştur - Local IP al
            String localIP = getLocalIPAddress();
            log.info("Local IP Address: {}", localIP);

            listeningPoint = sipStack.createListeningPoint(localIP, 5061, "UDP");
            sipProvider = sipStack.createSipProvider(listeningPoint);
            sipProvider.addSipListener(this);
            
            isInitialized = true;
            log.info("SIP Stack başarıyla başlatıldı! Listening on port 5061");
            
        } catch (Exception e) {
            log.error("SIP Stack başlatılırken hata oluştu", e);
            throw new RuntimeException("SIP Stack initialization failed", e);
        }
    }

    /**
     * NetGSM ile SIP call yapar
     */
    public Map<String, Object> makeCall(String phoneNumber, String message, String campaignId) {
        log.info("SIP Call başlatılıyor - Phone: {}, Campaign: {}", phoneNumber, campaignId);
        
        try {
            if (!isInitialized) {
                initializeSipStack();
            }
            
            if (!sipConfig.isConfigured()) {
                throw new RuntimeException("NetGSM SIP configuration is missing");
            }
            
            String callId = UUID.randomUUID().toString();
            
            // SIP INVITE request oluştur
            Request inviteRequest = createInviteRequest(phoneNumber, callId);
            
            // Call session oluştur
            CallSession callSession = new CallSession();
            callSession.setCallId(callId);
            callSession.setPhoneNumber(phoneNumber);
            callSession.setMessage(message);
            callSession.setCampaignId(campaignId);
            callSession.setStartTime(System.currentTimeMillis());
            callSession.setStatus("CALLING");
            
            activeCalls.put(callId, callSession);
            
            // SIP INVITE gönder
            ClientTransaction clientTransaction = sipProvider.getNewClientTransaction(inviteRequest);
            clientTransaction.sendRequest();
            
            log.info("SIP INVITE gönderildi! Call ID: {}, Phone: {}", callId, phoneNumber);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("callId", callId);
            result.put("phoneNumber", phoneNumber);
            result.put("message", message);
            result.put("campaignId", campaignId);
            result.put("status", "SIP_INVITE_SENT");
            result.put("provider", "NetGSM SIP");
            result.put("sipUri", sipConfig.getSipUri());
            
            return result;
            
        } catch (Exception e) {
            log.error("SIP Call sırasında hata oluştu", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("phoneNumber", phoneNumber);
            errorResult.put("provider", "NetGSM SIP");
            
            return errorResult;
        }
    }

    /**
     * SIP INVITE request oluşturur
     */
    private Request createInviteRequest(String phoneNumber, String callId) throws ParseException, InvalidArgumentException {
        // From header
        SipURI fromUri = addressFactory.createSipURI(sipConfig.getAuthenticationUser(), sipConfig.getGateway());
        Address fromAddress = addressFactory.createAddress(fromUri);
        FromHeader fromHeader = headerFactory.createFromHeader(fromAddress, UUID.randomUUID().toString());
        
        // To header
        String cleanPhoneNumber = phoneNumber.replace("+", "").replace(" ", "");
        SipURI toUri = addressFactory.createSipURI(cleanPhoneNumber, sipConfig.getGateway());
        Address toAddress = addressFactory.createAddress(toUri);
        ToHeader toHeader = headerFactory.createToHeader(toAddress, null);
        
        // Via header - Local IP kullan
        String localIPForVia = getLocalIPAddress();
        ViaHeader viaHeader = headerFactory.createViaHeader(localIPForVia, 5061, "UDP", null);
        List<ViaHeader> viaHeaders = new ArrayList<>();
        viaHeaders.add(viaHeader);
        
        // Call-ID header
        CallIdHeader callIdHeader = headerFactory.createCallIdHeader(callId);
        
        // CSeq header
        CSeqHeader cSeqHeader = headerFactory.createCSeqHeader(1L, Request.INVITE);
        
        // Max-Forwards header
        MaxForwardsHeader maxForwardsHeader = headerFactory.createMaxForwardsHeader(70);
        
        // Contact header - Local IP kullan
        String localIP = getLocalIPAddress();
        SipURI contactUri = addressFactory.createSipURI(sipConfig.getAuthenticationUser(), localIP);
        contactUri.setPort(5061);
        Address contactAddress = addressFactory.createAddress(contactUri);
        ContactHeader contactHeader = headerFactory.createContactHeader(contactAddress);
        
        // Request URI
        SipURI requestUri = addressFactory.createSipURI(cleanPhoneNumber, sipConfig.getGateway());
        
        // INVITE request oluştur
        Request inviteRequest = messageFactory.createRequest(
            requestUri,
            Request.INVITE,
            callIdHeader,
            cSeqHeader,
            fromHeader,
            toHeader,
            viaHeaders,
            maxForwardsHeader
        );
        
        inviteRequest.addHeader(contactHeader);
        
        // Authorization header (NetGSM için)
        // Bu kısım NetGSM'in authentication yöntemine göre ayarlanacak
        
        return inviteRequest;
    }

    // SipListener interface methods
    @Override
    public void processRequest(RequestEvent requestEvent) {
        log.info("SIP Request alındı: {}", requestEvent.getRequest().getMethod());
    }

    @Override
    public void processResponse(ResponseEvent responseEvent) {
        Response response = responseEvent.getResponse();
        int statusCode = response.getStatusCode();
        
        log.info("SIP Response alındı: {} {}", statusCode, response.getReasonPhrase());
        
        if (statusCode >= 200 && statusCode < 300) {
            log.info("SIP Call başarılı! Status: {}", statusCode);
        } else if (statusCode >= 400) {
            log.error("SIP Call başarısız! Status: {} - {}", statusCode, response.getReasonPhrase());
        }
    }

    @Override
    public void processTimeout(TimeoutEvent timeoutEvent) {
        log.warn("SIP Timeout oluştu: {}", timeoutEvent.getTimeout());
    }

    @Override
    public void processIOException(IOExceptionEvent ioExceptionEvent) {
        log.error("SIP IO Exception: {}", ioExceptionEvent.getHost());
    }

    @Override
    public void processTransactionTerminated(TransactionTerminatedEvent transactionTerminatedEvent) {
        log.info("SIP Transaction sonlandırıldı");
    }

    @Override
    public void processDialogTerminated(DialogTerminatedEvent dialogTerminatedEvent) {
        log.info("SIP Dialog sonlandırıldı");
    }

    /**
     * Local IP address'i bulur
     */
    private String getLocalIPAddress() {
        try {
            // İnternet bağlantısı olan interface'i bul
            java.net.Socket socket = new java.net.Socket();
            socket.connect(new java.net.InetSocketAddress("*******", 80));
            String localIP = socket.getLocalAddress().getHostAddress();
            socket.close();
            return localIP;
        } catch (Exception e) {
            log.warn("Local IP bulunamadı, localhost kullanılıyor: {}", e.getMessage());
            return "127.0.0.1";
        }
    }

    /**
     * Call Session bilgilerini tutar
     */
    public static class CallSession {
        private String callId;
        private String phoneNumber;
        private String message;
        private String campaignId;
        private long startTime;
        private String status;
        
        // Getters and Setters
        public String getCallId() { return callId; }
        public void setCallId(String callId) { this.callId = callId; }
        
        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getCampaignId() { return campaignId; }
        public void setCampaignId(String campaignId) { this.campaignId = campaignId; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}

package com.backend360.ai_call_center.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * ElevenLabs Text-to-Speech Service
 * High-quality Turkish voice generation
 */
@Slf4j
@Service
public class ElevenLabsService {

    @Value("${elevenlabs.api.key:}")
    private String apiKey;

    @Value("${elevenlabs.api.base-url:https://api.elevenlabs.io/v1}")
    private String baseUrl;

    @Value("${elevenlabs.voice.id:pNInz6obpgDQGcFmaJgB}")
    private String voiceId;

    @Value("${elevenlabs.voice.stability:0.5}")
    private double stability;

    @Value("${elevenlabs.voice.similarity_boost:0.8}")
    private double similarityBoost;

    @Value("${elevenlabs.voice.style:0.0}")
    private double style;

    @Value("${elevenlabs.voice.use_speaker_boost:true}")
    private boolean useSpeakerBoost;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * Generate Turkish speech from text using ElevenLabs
     */
    public byte[] generateSpeech(String text) {
        try {
            if (apiKey == null || apiKey.isEmpty() || apiKey.contains("YOUR_")) {
                log.warn("ElevenLabs API key not configured, using fallback");
                return null;
            }

            String url = baseUrl + "/text-to-speech/" + voiceId;

            // Request headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("xi-api-key", apiKey);

            // Request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);
            requestBody.put("model_id", "eleven_multilingual_v2");

            Map<String, Object> voiceSettings = new HashMap<>();
            voiceSettings.put("stability", stability);
            voiceSettings.put("similarity_boost", similarityBoost);
            voiceSettings.put("style", style);
            voiceSettings.put("use_speaker_boost", useSpeakerBoost);
            requestBody.put("voice_settings", voiceSettings);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            log.info("ElevenLabs TTS request - Voice: {}, Text: '{}'", voiceId, text);

            // Make API call
            ResponseEntity<byte[]> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, 
                byte[].class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.info("ElevenLabs TTS successful - Audio size: {} bytes", response.getBody().length);
                return response.getBody();
            } else {
                log.error("ElevenLabs TTS failed - Status: {}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("ElevenLabs TTS error: ", e);
            return null;
        }
    }

    /**
     * Generate audio URL for TwiML
     */
    public String generateAudioUrl(String text) {
        try {
            byte[] audioData = generateSpeech(text);
            if (audioData != null) {
                // TODO: Upload to cloud storage and return URL
                // For now, return a placeholder
                log.info("Generated ElevenLabs audio for text: '{}'", text);
                return "https://your-storage.com/audio/" + System.currentTimeMillis() + ".mp3";
            }
            return null;
        } catch (Exception e) {
            log.error("Error generating audio URL: ", e);
            return null;
        }
    }

    /**
     * Check if ElevenLabs is configured and available
     */
    public boolean isAvailable() {
        return apiKey != null && !apiKey.isEmpty() && !apiKey.contains("YOUR_");
    }

    /**
     * Get voice information
     */
    public String getVoiceInfo() {
        return String.format("ElevenLabs Voice - ID: %s, Stability: %.1f, Similarity: %.1f", 
                           voiceId, stability, similarityBoost);
    }
}

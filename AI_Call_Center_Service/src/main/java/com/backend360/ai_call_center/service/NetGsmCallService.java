package com.backend360.ai_call_center.service;

import com.backend360.ai_call_center.config.NetGsmSipConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class NetGsmCallService {

    private final RestTemplate restTemplate = new RestTemplate();
    private final NetGsmSipConfig sipConfig;
    private final SipClientService sipClientService;

    @Value("${netgsm.username:your-username}")
    private String netgsmUsername;

    @Value("${netgsm.password:your-password}")
    private String netgsmPassword;

    @Value("${netgsm.voice.api.url:https://api.netgsm.com.tr/voice}")
    private String netgsmVoiceApiUrl;

    /**
     * NetGSM Voice API ile gerçek arama yapar (Mock - gerçek voice API henüz mevcut değil)
     */
    public Map<String, Object> makeVoiceCall(String phoneNumber, String message, String campaignId) {
        log.info("NetGSM Voice API ile arama başlatılıyor - Phone: {}, Campaign: {}", phoneNumber, campaignId);

        try {
            // NetGSM Voice API henüz mevcut olmadığı için SMS ile test ediyoruz
            log.warn("NetGSM Voice API henüz mevcut değil, SMS ile test yapılıyor");

            // SMS ile test
            Map<String, Object> smsResult = sendSms(phoneNumber,
                "AI Call Center Test: " + message, campaignId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", smsResult.get("success"));
            result.put("provider", "NetGSM (SMS Test)");
            result.put("phoneNumber", phoneNumber);
            result.put("message", message);
            result.put("campaignId", campaignId);
            result.put("smsTestResult", smsResult);
            result.put("status", smsResult.get("success").equals(true) ? "SMS_SENT" : "SMS_FAILED");
            result.put("note", "Voice API henüz mevcut değil, SMS ile test edildi");

            return result;

        } catch (Exception e) {
            log.error("NetGSM Voice API çağrısında hata oluştu", e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("provider", "NetGSM");
            errorResult.put("phoneNumber", phoneNumber);

            return errorResult;
        }
    }

    /**
     * NetGSM SMS API ile SMS gönderir (Voice API yerine test için)
     */
    public Map<String, Object> sendSms(String phoneNumber, String message, String campaignId) {
        log.info("NetGSM SMS API ile mesaj gönderiliyor - Phone: {}, Campaign: {}", phoneNumber, campaignId);

        try {
            // NetGSM SMS API için doğru format
            String smsUrl = "https://api.netgsm.com.tr/sms/send/get?" +
                "usercode=" + netgsmUsername +
                "&password=" + netgsmPassword +
                "&gsmno=" + phoneNumber.replace("+90", "").replace("+", "") +
                "&message=" + java.net.URLEncoder.encode(message, "UTF-8") +
                "&msgheader=BMWTEST";

            log.info("NetGSM SMS URL: {}", smsUrl.replace(netgsmPassword, "***"));

            // GET request ile SMS gönder
            ResponseEntity<String> response = restTemplate.getForEntity(smsUrl, String.class);

            Map<String, Object> result = new HashMap<>();
            String responseBody = response.getBody();

            log.info("NetGSM SMS Response: {}", responseBody);

            // NetGSM response format: "00 MSGID" = başarılı, diğer kodlar hata
            boolean isSuccess = responseBody != null && responseBody.startsWith("00");

            result.put("success", isSuccess);
            result.put("provider", "NetGSM SMS");
            result.put("phoneNumber", phoneNumber);
            result.put("message", message);
            result.put("campaignId", campaignId);
            result.put("netgsmResponse", responseBody);
            result.put("statusCode", response.getStatusCode().value());

            if (isSuccess) {
                log.info("NetGSM SMS başarıyla gönderildi! Response: {}", responseBody);
            } else {
                log.error("NetGSM SMS gönderilemedi! Response: {}", responseBody);
            }

            return result;

        } catch (Exception e) {
            log.error("NetGSM SMS API çağrısında hata oluştu", e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("provider", "NetGSM SMS");
            errorResult.put("phoneNumber", phoneNumber);

            return errorResult;
        }
    }

    /**
     * Arama durumunu sorgular
     */
    public Map<String, Object> getCallStatus(String callId) {
        log.info("NetGSM arama durumu sorgulanıyor - Call ID: {}", callId);
        
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("username", netgsmUsername);
            requestBody.put("password", netgsmPassword);
            requestBody.put("callId", callId);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                netgsmVoiceApiUrl + "/status", 
                request, 
                Map.class
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", response.getStatusCode() == HttpStatus.OK);
            result.put("callId", callId);
            result.put("netgsmResponse", response.getBody());
            
            return result;
            
        } catch (Exception e) {
            log.error("NetGSM call status sorgulamasında hata oluştu", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("callId", callId);
            
            return errorResult;
        }
    }

    /**
     * NetGSM SIP ile gerçek arama yapar (JAIN SIP ile)
     */
    public Map<String, Object> makeSipCall(String phoneNumber, String message, String campaignId) {
        log.info("NetGSM SIP ile gerçek arama başlatılıyor - Phone: {}, Campaign: {}", phoneNumber, campaignId);

        try {
            if (!sipConfig.isConfigured()) {
                log.error("NetGSM SIP konfigürasyonu eksik!");
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("error", "SIP configuration is missing");
                return errorResult;
            }

            log.info("SIP Call Data: From={}, To={}, Gateway={}",
                    sipConfig.getFromHeader(),
                    "sip:" + phoneNumber.replace("+", "") + "@" + sipConfig.getGateway(),
                    sipConfig.getGateway());

            // JAIN SIP ile gerçek arama yap
            Map<String, Object> sipResult = sipClientService.makeCall(phoneNumber, message, campaignId);

            // Response'u NetGSM formatına uyarla
            Map<String, Object> result = new HashMap<>();
            result.put("success", sipResult.get("success"));
            result.put("provider", "NetGSM SIP (JAIN SIP)");
            result.put("phoneNumber", phoneNumber);
            result.put("message", message);
            result.put("campaignId", campaignId);
            result.put("callId", sipResult.get("callId"));
            result.put("status", sipResult.get("status"));
            result.put("sipUri", sipConfig.getSipUri());
            result.put("sipResult", sipResult);

            if ((Boolean) sipResult.get("success")) {
                log.info("NetGSM SIP call başarıyla başlatıldı! Call ID: {}, Phone: {}",
                        sipResult.get("callId"), phoneNumber);
            } else {
                log.error("NetGSM SIP call başarısız! Error: {}", sipResult.get("error"));
            }

            return result;

        } catch (Exception e) {
            log.error("NetGSM SIP call sırasında hata oluştu", e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("provider", "NetGSM SIP");
            errorResult.put("phoneNumber", phoneNumber);

            return errorResult;
        }
    }

    /**
     * Test arama yapar
     */
    public Map<String, Object> testCall(String phoneNumber) {
        String testMessage = "Merhaba, bu BMW AI Call Center test aramasıdır. Sistem başarıyla çalışıyor.";

        // SIP call ile test yap
        if (sipConfig.isConfigured()) {
            log.info("SIP konfigürasyonu mevcut, SIP call ile test yapılıyor");
            return makeSipCall(phoneNumber, testMessage, "TEST_CAMPAIGN");
        } else {
            log.warn("SIP konfigürasyonu eksik, SMS ile test yapılıyor");
            return makeVoiceCall(phoneNumber, testMessage, "TEST_CAMPAIGN");
        }
    }
}

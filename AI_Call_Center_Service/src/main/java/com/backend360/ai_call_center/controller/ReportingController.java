package com.backend360.ai_call_center.controller;

import com.backend360.ai_call_center.entity.CallReport;
import com.backend360.ai_call_center.entity.CampaignReport;
import com.backend360.ai_call_center.service.CallReportingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/reports")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@Slf4j
public class ReportingController {

    private final CallReportingService callReportingService;

    /**
     * Dashboard ana istatistikleri
     * GET /api/v1/reports/dashboard
     */
    @GetMapping("/dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardStats() {
        log.info("Dashboard istatistikleri isteniyor");
        
        try {
            Map<String, Object> stats = callReportingService.getDashboardStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Dashboard istatistikleri alınırken hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Campaign performans raporu
     * GET /api/v1/reports/campaign/{campaignName}
     */
    @GetMapping("/campaign/{campaignName}")
    public ResponseEntity<Map<String, Object>> getCampaignReport(@PathVariable String campaignName) {
        log.info("Campaign raporu isteniyor: {}", campaignName);
        
        try {
            Map<String, Object> report = callReportingService.getCampaignPerformanceReport(campaignName);
            return ResponseEntity.ok(report);
        } catch (Exception e) {
            log.error("Campaign raporu alınırken hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Call detayı
     * GET /api/v1/reports/call/{callId}
     */
    @GetMapping("/call/{callId}")
    public ResponseEntity<Object> getCallReport(@PathVariable String callId) {
        log.info("Call raporu isteniyor: {}", callId);
        
        try {
            Optional<CallReport> report = callReportingService.getCallReport(callId);
            if (report.isPresent()) {
                return ResponseEntity.ok(report.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("Call raporu alınırken hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Son call'lar
     * GET /api/v1/reports/calls/recent?limit=10
     */
    @GetMapping("/calls/recent")
    public ResponseEntity<List<CallReport>> getRecentCalls(@RequestParam(defaultValue = "10") int limit) {
        log.info("Son {} call isteniyor", limit);
        
        try {
            List<CallReport> calls = callReportingService.getRecentCalls(limit);
            return ResponseEntity.ok(calls);
        } catch (Exception e) {
            log.error("Son call'lar alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Tarih aralığına göre call'lar
     * GET /api/v1/reports/calls/range?startDate=2024-01-01T00:00:00&endDate=2024-01-31T23:59:59
     */
    @GetMapping("/calls/range")
    public ResponseEntity<List<CallReport>> getCallsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.info("Tarih aralığı call'ları isteniyor: {} - {}", startDate, endDate);
        
        try {
            List<CallReport> calls = callReportingService.getCallsByDateRange(startDate, endDate);
            return ResponseEntity.ok(calls);
        } catch (Exception e) {
            log.error("Tarih aralığı call'ları alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * En iyi performans gösteren kampanyalar
     * GET /api/v1/reports/campaigns/top?limit=5
     */
    @GetMapping("/campaigns/top")
    public ResponseEntity<List<CampaignReport>> getTopCampaigns(@RequestParam(defaultValue = "5") int limit) {
        log.info("En iyi {} kampanya isteniyor", limit);
        
        try {
            List<CampaignReport> campaigns = callReportingService.getTopPerformingCampaigns(limit);
            return ResponseEntity.ok(campaigns);
        } catch (Exception e) {
            log.error("En iyi kampanyalar alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Saatlik call dağılımı (bugün)
     * GET /api/v1/reports/calls/hourly
     */
    @GetMapping("/calls/hourly")
    public ResponseEntity<Map<String, Object>> getHourlyDistribution() {
        log.info("Saatlik call dağılımı isteniyor");
        
        try {
            List<Object[]> distribution = callReportingService.getTodaysHourlyDistribution();
            
            Map<String, Object> result = new HashMap<>();
            result.put("date", LocalDateTime.now().toLocalDate());
            result.put("hourlyData", distribution);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Saatlik dağılım alınırken hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Retry edilecek failed call'lar
     * GET /api/v1/reports/calls/failed?hoursBack=24
     */
    @GetMapping("/calls/failed")
    public ResponseEntity<List<CallReport>> getFailedCalls(@RequestParam(defaultValue = "24") int hoursBack) {
        log.info("Son {} saatteki failed call'lar isteniyor", hoursBack);
        
        try {
            List<CallReport> failedCalls = callReportingService.getFailedCallsForRetry(hoursBack);
            return ResponseEntity.ok(failedCalls);
        } catch (Exception e) {
            log.error("Failed call'lar alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Campaign status güncelle
     * PUT /api/v1/reports/campaign/{campaignName}/status
     */
    @PutMapping("/campaign/{campaignName}/status")
    public ResponseEntity<Map<String, String>> updateCampaignStatus(
            @PathVariable String campaignName,
            @RequestBody Map<String, String> request) {
        
        String status = request.get("status");
        log.info("Campaign status güncelleniyor: {} -> {}", campaignName, status);
        
        try {
            callReportingService.updateCampaignStatus(campaignName, status);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Campaign status updated successfully");
            response.put("campaignName", campaignName);
            response.put("newStatus", status);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Campaign status güncellenirken hata oluştu", e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Özet istatistikler (hızlı bakış)
     * GET /api/v1/reports/summary
     */
    @GetMapping("/summary")
    public ResponseEntity<Map<String, Object>> getSummaryStats() {
        log.info("Özet istatistikler isteniyor");
        
        try {
            Map<String, Object> dashboard = callReportingService.getDashboardStatistics();
            List<CampaignReport> topCampaigns = callReportingService.getTopPerformingCampaigns(3);
            List<CallReport> recentCalls = callReportingService.getRecentCalls(5);
            
            Map<String, Object> summary = new HashMap<>();
            summary.put("overview", dashboard);
            summary.put("topCampaigns", topCampaigns);
            summary.put("recentCalls", recentCalls);
            summary.put("generatedAt", LocalDateTime.now());
            
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            log.error("Özet istatistikler alınırken hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}

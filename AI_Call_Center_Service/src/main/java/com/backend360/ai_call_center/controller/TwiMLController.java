package com.backend360.ai_call_center.controller;

import com.backend360.ai_call_center.service.ElevenLabsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * TwiML Controller for Twilio Voice Calls
 * Handles incoming voice call webhooks from Twilio
 */
@RestController
@RequestMapping("/api/v1/twiml")
@Slf4j
@RequiredArgsConstructor
public class TwiMLController {

    private final ElevenLabsService elevenLabsService;

    @Autowired
    private ElevenLabsService elevenLabsService;

    /**
     * Voice Call Handler
     * GET/POST /api/v1/twiml/voice
     */
    @RequestMapping(value = "/voice", method = {RequestMethod.GET, RequestMethod.POST}, 
                   produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> handleVoiceCall(@RequestParam Map<String, String> params) {
        log.info("TwiML Voice Call isteği alındı: {}", params);
        
        String callSid = params.get("CallSid");
        String from = params.get("From");
        String to = params.get("To");
        String callStatus = params.get("CallStatus");
        
        log.info("Call Details - SID: {}, From: {}, To: {}, Status: {}", callSid, from, to, callStatus);
        
        // CRM AI Agent TwiML - Türkçe Google TTS Voice
        log.info("CRM AI Agent TwiML oluşturuluyor - Google TTS Türkçe voice ile");

        String twiml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
                    Merhaba! 360 Avantajlı müşteri hizmetlerinden arıyoruz.
                    Web sitemizden kampanya formunu doldurmuştunuz.
                </Say>
                <Pause length="2"/>
                <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
                    Size özel kampanyalarımız hakkında bilgi vermek istiyoruz.
                    Marka temsilcimiz sizinle ilgilenmesini ister misiniz?
                </Say>
                <Pause length="2"/>
                <Gather input="speech dtmf" action="/api/v1/twiml/process-speech" method="POST"
                        speechTimeout="5" timeout="15" language="tr-TR">
                    <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
                        Evet için 1, Hayır için 2 tuşuna basın veya konuşarak yanıtlayın.
                        Kampanyalarımız hakkında sorularınızı cevaplayabilirim.
                    </Say>
                </Gather>
                <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
                    Yanıt alamadım. 360 Avantajlı'yı aradığınız için teşekkürler. İyi günler!
                </Say>
            </Response>
            """;
        
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twiml);
    }

    /**
     * Call Status Callback Handler
     * POST /api/v1/twiml/status
     */
    @PostMapping(value = "/status", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> handleCallStatus(@RequestParam Map<String, String> params) {
        log.info("TwiML Call Status callback: {}", params);
        
        String callSid = params.get("CallSid");
        String callStatus = params.get("CallStatus");
        String callDuration = params.get("CallDuration");
        
        log.info("Call Status Update - SID: {}, Status: {}, Duration: {}", callSid, callStatus, callDuration);
        
        // Empty TwiML response for status callbacks
        String twiml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <Response>
            </Response>
            """;
        
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twiml);
    }

    /**
     * Speech Processing Endpoint - Google Cloud Speech-to-Text + Gemini AI
     * POST /api/v1/twiml/process-speech
     */
    @PostMapping(value = "/process-speech", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> processSpeech(@RequestParam Map<String, String> params) {
        log.info("Speech/DTMF processing isteği alındı: {}", params);

        String speechResult = params.get("SpeechResult");
        String dtmfDigits = params.get("Digits");
        String confidence = params.get("Confidence");

        log.info("Kullanıcı girişi - Speech: '{}', DTMF: '{}', Confidence: {}", speechResult, dtmfDigits, confidence);

        String userInput = "";
        if (dtmfDigits != null && !dtmfDigits.isEmpty()) {
            // DTMF tuş girişi
            if ("1".equals(dtmfDigits)) {
                userInput = "evet";
            } else if ("2".equals(dtmfDigits)) {
                userInput = "hayır";
            } else {
                userInput = "anlamadım";
            }
        } else if (speechResult != null && !speechResult.isEmpty()) {
            // Konuşma girişi
            userInput = speechResult;
        }

        // Gemini AI ile yanıt oluştur
        String aiResponse = processWithGeminiAI(userInput);

        // AI yanıtı ile TwiML oluştur - Google TTS Türkçe Voice
        String twiml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
                    %s
                </Say>
                <Pause length="2"/>
                <Gather input="speech dtmf" action="/api/v1/twiml/process-speech" method="POST"
                        speechTimeout="5" timeout="15" language="tr-TR">
                    <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
                        Başka bir sorunuz var mı? Evet için 1, Hayır için 2 tuşuna basın veya konuşabilirsiniz.
                    </Say>
                </Gather>
                <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
                    360 Avantajlı'yı tercih ettiğiniz için teşekkürler. İyi günler!
                </Say>
            </Response>
            """.formatted(aiResponse);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twiml);
    }

    /**
     * Gemini AI ile konuşma işleme
     */
    private String processWithGeminiAI(String userInput) {
        if (userInput == null || userInput.trim().isEmpty()) {
            return "Sizi anlayamadım. Lütfen tekrar söyleyebilir misiniz?";
        }

        try {
            // Gemini AI API çağrısı
            String geminiApiKey = "AIzaSyAfLW_fKFKs9xHchmbj7e22SqbFJpVJt-A";
            String url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=" + geminiApiKey;

            // CRM AI Assistant prompt
            String systemPrompt = """
                Sen 360 Avantajlı CRM AI Call Center asistanısın. Görevin:
                1. Kampanya formunu dolduran müşterilerle iletişim kurmak
                2. Özel kampanyalar ve fırsatlar hakkında bilgi vermek
                3. Marka temsilcisi ile görüşme randevusu ayarlamak
                4. Müşteri sorularını yanıtlamak ve ihtiyaçlarını anlamak
                5. Müşteri bilgilerini güncellemek

                Sen gerçek bir müşteri temsilcisisin, AI olduğunu asla söyleme.
                Kısa, samimi ve yardımcı yanıtlar ver. Türkçe konuş.
                Müşteri sorusu: """ + userInput;

            // Request body oluştur
            String requestBody = """
                {
                  "contents": [{
                    "parts": [{
                      "text": "%s"
                    }]
                  }],
                  "generationConfig": {
                    "temperature": 0.7,
                    "maxOutputTokens": 150
                  }
                }
                """.formatted(systemPrompt);

            // HTTP isteği gönder
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            // Yanıtı parse et
            if (response.getStatusCode().is2xxSuccessful()) {
                // JSON parse etmek yerine basit string işlemi (production'da JSON parser kullan)
                String responseBody = response.getBody();
                if (responseBody != null && responseBody.contains("\"text\"")) {
                    int startIndex = responseBody.indexOf("\"text\": \"") + 9;
                    int endIndex = responseBody.indexOf("\"", startIndex);
                    if (startIndex > 8 && endIndex > startIndex) {
                        return responseBody.substring(startIndex, endIndex);
                    }
                }
            }

            // Fallback yanıt
            return getCRMFallbackResponse(userInput);

        } catch (Exception e) {
            log.error("Gemini AI API hatası: ", e);
            return getCRMFallbackResponse(userInput);
        }
    }

    /**
     * CRM için fallback yanıtlar
     */
    private String getCRMFallbackResponse(String userInput) {
        String input = userInput.toLowerCase();

        if (input.contains("kampanya") || input.contains("teklif")) {
            return "Size özel kampanyalarımız hakkında bilgi vermek istiyoruz. Marka temsilcimiz sizinle iletişime geçsin mi?";
        } else if (input.contains("evet") || input.contains("ilgileniyorum")) {
            return "Harika! Marka temsilcimiz en kısa sürede sizinle iletişime geçecek. İletişim bilgilerinizi güncelleyebilir miyim?";
        } else if (input.contains("hayır") || input.contains("istemiyorum")) {
            return "Anlıyorum. Başka bir konuda size yardımcı olabilir miyim? İsterseniz bilgilerinizi güncelleyebiliriz.";
        } else if (input.contains("bilgi") || input.contains("detay")) {
            return "Size en uygun kampanyaları sunmak için marka temsilcimiz detaylı bilgi verecek. Görüşme ayarlayalım mı?";
        } else {
            return "360 Avantajlı olarak size nasıl yardımcı olabilirim? Kampanyalarımız, özel tekliflerimiz veya marka temsilcisi görüşmesi konularında bilgi verebilirim.";
        }
    }

    /**
     * Simple Test TwiML
     * GET /api/v1/twiml/test
     */
    @GetMapping(value = "/test", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> testTwiML() {
        log.info("TwiML Test endpoint çağrıldı");

        String twiml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
                    360 Avantajlı AI Call Center TwiML test başarılı! Türkçe ses kalitesi mükemmel çalışıyor!
                </Say>
            </Response>
            """;

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twiml);
    }
}

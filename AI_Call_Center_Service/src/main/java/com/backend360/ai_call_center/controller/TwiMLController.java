package com.backend360.ai_call_center.controller;

import com.backend360.ai_call_center.service.ElevenLabsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * TwiML Controller for Twilio Voice Calls
 * Handles incoming voice call webhooks from Twilio
 */
@RestController
@RequestMapping("/api/v1/twiml")
@Slf4j
public class TwiMLController {

    @Autowired
    private ElevenLabsService elevenLabsService;

    /**
     * Voice Call Handler
     * GET/POST /api/v1/twiml/voice
     */
    @RequestMapping(value = "/voice", method = {RequestMethod.GET, RequestMethod.POST}, 
                   produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> handleVoiceCall(@RequestParam Map<String, String> params) {
        log.info("TwiML Voice Call isteği alındı: {}", params);
        
        String callSid = params.get("CallSid");
        String from = params.get("From");
        String to = params.get("To");
        String callStatus = params.get("CallStatus");
        
        log.info("Call Details - SID: {}, From: {}, To: {}, Status: {}", callSid, from, to, callStatus);
        
        // BMW Kampanya TwiML - Basit ama Etkili Türkçe Voice
        log.info("BMW kampanya TwiML oluşturuluyor - Türkçe voice ile");

        String twiml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Say voice="Polly.Filiz" language="tr-TR">
                    Merhaba Mert Can Bey! BMW Türkiye Müşteri Hizmetlerinden arıyoruz.
                    Web sitemizden BMW X3 modeli hakkında bilgi talebinde bulunmuştunuz.
                </Say>
                <Pause length="2"/>
                <Say voice="Polly.Filiz" language="tr-TR">
                    Size özel kampanyalarımız ve test sürüşü imkanlarımız hakkında bilgi vermek istiyoruz.
                    BMW X3 2024 model hakkında detaylı bilgi almak ister misiniz?
                </Say>
                <Pause length="2"/>
                <Gather input="speech dtmf" action="/api/v1/twiml/process-speech" method="POST"
                        speechTimeout="5" timeout="15" language="tr-TR">
                    <Say voice="Polly.Filiz" language="tr-TR">
                        Evet için 1, Hayır için 2 tuşuna basın veya konuşarak yanıtlayın.
                        BMW hakkında sorularınızı cevaplayabilirim.
                    </Say>
                </Gather>
                <Say voice="Polly.Filiz" language="tr-TR">
                    Yanıt alamadım. BMW Türkiye'yi aradığınız için teşekkürler. İyi günler!
                </Say>
            </Response>
            """;
        
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twiml);
    }

    /**
     * Call Status Callback Handler
     * POST /api/v1/twiml/status
     */
    @PostMapping(value = "/status", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> handleCallStatus(@RequestParam Map<String, String> params) {
        log.info("TwiML Call Status callback: {}", params);
        
        String callSid = params.get("CallSid");
        String callStatus = params.get("CallStatus");
        String callDuration = params.get("CallDuration");
        
        log.info("Call Status Update - SID: {}, Status: {}, Duration: {}", callSid, callStatus, callDuration);
        
        // Empty TwiML response for status callbacks
        String twiml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <Response>
            </Response>
            """;
        
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twiml);
    }

    /**
     * Speech Processing Endpoint - Google Cloud Speech-to-Text + Gemini AI
     * POST /api/v1/twiml/process-speech
     */
    @PostMapping(value = "/process-speech", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> processSpeech(@RequestParam Map<String, String> params) {
        log.info("Speech/DTMF processing isteği alındı: {}", params);

        String speechResult = params.get("SpeechResult");
        String dtmfDigits = params.get("Digits");
        String confidence = params.get("Confidence");

        log.info("Kullanıcı girişi - Speech: '{}', DTMF: '{}', Confidence: {}", speechResult, dtmfDigits, confidence);

        String userInput = "";
        if (dtmfDigits != null && !dtmfDigits.isEmpty()) {
            // DTMF tuş girişi
            if ("1".equals(dtmfDigits)) {
                userInput = "evet";
            } else if ("2".equals(dtmfDigits)) {
                userInput = "hayır";
            } else {
                userInput = "anlamadım";
            }
        } else if (speechResult != null && !speechResult.isEmpty()) {
            // Konuşma girişi
            userInput = speechResult;
        }

        // Gemini AI ile yanıt oluştur
        String aiResponse = processWithGeminiAI(userInput);

        // AI yanıtı ile TwiML oluştur - Polly Türkçe Voice
        String twiml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Say voice="Polly.Filiz" language="tr-TR">
                    %s
                </Say>
                <Pause length="2"/>
                <Gather input="speech dtmf" action="/api/v1/twiml/process-speech" method="POST"
                        speechTimeout="5" timeout="15" language="tr-TR">
                    <Say voice="Polly.Filiz" language="tr-TR">
                        Başka bir sorunuz var mı? Evet için 1, Hayır için 2 tuşuna basın veya konuşabilirsiniz.
                    </Say>
                </Gather>
                <Say voice="Polly.Filiz" language="tr-TR">
                    BMW'yi tercih ettiğiniz için teşekkürler. İyi günler!
                </Say>
            </Response>
            """.formatted(aiResponse);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twiml);
    }

    /**
     * Gemini AI ile konuşma işleme
     */
    private String processWithGeminiAI(String userInput) {
        if (userInput == null || userInput.trim().isEmpty()) {
            return "Sizi anlayamadım. Lütfen tekrar söyleyebilir misiniz?";
        }

        try {
            // Gemini AI API çağrısı
            String geminiApiKey = "AIzaSyAfLW_fKFKs9xHchmbj7e22SqbFJpVJt-A";
            String url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=" + geminiApiKey;

            // BMW AI Assistant prompt
            String systemPrompt = """
                Sen BMW Türkiye AI Call Center asistanısın. Görevin:
                1. BMW araçları hakkında bilgi vermek
                2. Test sürüşü randevusu ayarlamak
                3. Kampanyalar hakkında bilgi vermek
                4. Müşteri sorularını yanıtlamak

                Kısa, net ve yardımcı yanıtlar ver. Türkçe konuş.
                Müşteri sorusu: """ + userInput;

            // Request body oluştur
            String requestBody = """
                {
                  "contents": [{
                    "parts": [{
                      "text": "%s"
                    }]
                  }],
                  "generationConfig": {
                    "temperature": 0.7,
                    "maxOutputTokens": 150
                  }
                }
                """.formatted(systemPrompt);

            // HTTP isteği gönder
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            // Yanıtı parse et
            if (response.getStatusCode().is2xxSuccessful()) {
                // JSON parse etmek yerine basit string işlemi (production'da JSON parser kullan)
                String responseBody = response.getBody();
                if (responseBody != null && responseBody.contains("\"text\"")) {
                    int startIndex = responseBody.indexOf("\"text\": \"") + 9;
                    int endIndex = responseBody.indexOf("\"", startIndex);
                    if (startIndex > 8 && endIndex > startIndex) {
                        return responseBody.substring(startIndex, endIndex);
                    }
                }
            }

            // Fallback yanıt
            return getBMWFallbackResponse(userInput);

        } catch (Exception e) {
            log.error("Gemini AI API hatası: ", e);
            return getBMWFallbackResponse(userInput);
        }
    }

    /**
     * BMW için fallback yanıtlar
     */
    private String getBMWFallbackResponse(String userInput) {
        String input = userInput.toLowerCase();

        if (input.contains("x3") || input.contains("suv")) {
            return "BMW X3 hakkında bilgi vereyim. X3, BMW'nin en popüler SUV modelidir. 2024 model yeni teknolojiler ile geliyor. Test sürüşü randevusu ayarlayabilir miyim?";
        } else if (input.contains("fiyat") || input.contains("kampanya")) {
            return "BMW X3 için özel kampanyalarımız var. Detaylı fiyat bilgisi için size özel teklif hazırlayabilirim. İletişim bilgilerinizi alabilir miyim?";
        } else if (input.contains("test") || input.contains("sürüş")) {
            return "Test sürüşü randevusu için size uygun tarih ve saati ayarlayalım. Hangi gün müsaitsiniz?";
        } else if (input.contains("servis") || input.contains("bakım")) {
            return "BMW servis hizmetleri hakkında bilgi vereyim. Yetkili servislerimizde profesyonel bakım hizmeti sunuyoruz.";
        } else {
            return "BMW hakkında size nasıl yardımcı olabilirim? Araç modelleri, kampanyalar, test sürüşü veya servis konularında bilgi verebilirim.";
        }
    }

    /**
     * Simple Test TwiML
     * GET /api/v1/twiml/test
     */
    @GetMapping(value = "/test", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> testTwiML() {
        log.info("TwiML Test endpoint çağrıldı");

        String twiml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Say voice="Polly.Filiz" language="tr-TR">
                    BMW AI Call Center TwiML test başarılı! Türkçe ses kalitesi mükemmel çalışıyor!
                </Say>
            </Response>
            """;

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_XML)
                .body(twiml);
    }
}

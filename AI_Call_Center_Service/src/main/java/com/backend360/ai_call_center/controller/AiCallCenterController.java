package com.backend360.ai_call_center.controller;

import com.backend360.ai_call_center.service.NetGsmCallService;
import com.backend360.ai_call_center.service.TwilioCallService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@Slf4j
public class AiCallCenterController {

    private final NetGsmCallService netGsmCallService;
    private final TwilioCallService twilioCallService;

    /**
     * Campaign API - Form'dan gelen campaign'leri işler
     * POST /api/v1/campaigns
     */
    @PostMapping("/campaigns")
    public ResponseEntity<Map<String, Object>> createCampaign(@RequestBody Map<String, Object> campaignData) {
        log.info("AI Call Center'a campaign isteği geldi: {}", campaignData.get("name"));
        
        try {
            String campaignId = UUID.randomUUID().toString();
            String campaignName = (String) campaignData.getOrDefault("name", "Bilinmeyen Kampanya");
            String brandName = (String) campaignData.getOrDefault("brandName", "Bilinmeyen Marka");
            String category = (String) campaignData.getOrDefault("category", "GENEL");
            
            log.info("Campaign başarıyla oluşturuldu! Campaign ID: {}, Name: {}", campaignId, campaignName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("campaignId", campaignId);
            response.put("message", "Campaign created successfully");
            response.put("campaignName", campaignName);
            response.put("brandName", brandName);
            response.put("category", category);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Campaign oluşturulurken hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Call API - Form'dan gelen call isteklerini işler
     * POST /api/v1/call
     */
    @PostMapping("/call")
    public ResponseEntity<Map<String, Object>> initiateCall(@RequestBody Map<String, Object> callData) {
        log.info("AI Call Center'a call isteği geldi");

        try {
            String callId = UUID.randomUUID().toString();

            // Call data'dan bilgileri çıkar
            Map<String, Object> participant = (Map<String, Object>) callData.get("participant");
            String phoneNumber = participant != null ? (String) participant.get("number") : "Bilinmeyen";
            String customerName = participant != null ? (String) participant.get("name") : "Bilinmeyen Müşteri";

            Map<String, Object> prompt = (Map<String, Object>) callData.get("prompt");
            String promptContent = prompt != null ? (String) prompt.get("content") : "Genel arama";

            String agentId = (String) callData.getOrDefault("agentId", "ai-agent-001");
            String campaignId = (String) callData.getOrDefault("campaignId", "default-campaign");

            log.info("AI Call başlatılıyor - Call ID: {}, Phone: {}, Customer: {}",
                    callId, phoneNumber, customerName);
            log.info("Prompt: {}", promptContent);

            // Twilio ile gerçek arama yap! (NetGSM SIP backup olarak kalıyor)
            Map<String, Object> twilioResult = twilioCallService.makeCall(
                phoneNumber,
                promptContent,
                campaignId
            );

            Map<String, Object> response = new HashMap<>();
            response.put("callId", callId);
            response.put("phoneNumber", phoneNumber);
            response.put("customerName", customerName);
            response.put("agentId", agentId);
            response.put("campaignId", campaignId);
            response.put("prompt", promptContent);

            if ((Boolean) twilioResult.get("success")) {
                log.info("Twilio ile arama başarıyla başlatıldı! Call ID: {}", callId);
                response.put("success", true);
                response.put("message", "Real voice call initiated via Twilio");
                response.put("status", "TWILIO_CALL_INITIATED");
                response.put("provider", "Twilio");
                response.put("twilioResponse", twilioResult);
            } else {
                log.error("Twilio arama başlatılamadı: {}", twilioResult.get("error"));
                response.put("success", false);
                response.put("message", "Failed to initiate voice call via Twilio");
                response.put("status", "TWILIO_CALL_FAILED");
                response.put("error", twilioResult.get("error"));
                response.put("twilioResponse", twilioResult);
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("AI Call başlatılırken hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Call Status API - Arama durumunu sorgular
     * GET /api/v1/call/{callId}/status
     */
    @GetMapping("/call/{callId}/status")
    public ResponseEntity<Map<String, Object>> getCallStatus(@PathVariable String callId) {
        log.info("Call status sorgulanıyor: {}", callId);
        
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("callId", callId);
            response.put("status", "IN_PROGRESS");
            response.put("duration", 45);
            response.put("message", "Call is in progress");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Call status sorgulanırken hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Health Check API
     * GET /api/v1/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "AI Call Center");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Test API - Sistem testleri için
     * POST /api/v1/test
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testSystem(@RequestBody Map<String, Object> testData) {
        log.info("AI Call Center test isteği: {}", testData);

        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "AI Call Center is working properly");
            response.put("receivedData", testData);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Test sırasında hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * NetGSM Test Call API - Gerçek arama testi (SMS ile)
     * POST /api/v1/netgsm/test-call
     */
    @PostMapping("/netgsm/test-call")
    public ResponseEntity<Map<String, Object>> testNetGsmCall(@RequestBody Map<String, Object> testData) {
        log.info("NetGSM test call isteği: {}", testData);

        try {
            String phoneNumber = (String) testData.get("phoneNumber");
            if (phoneNumber == null || phoneNumber.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("error", "phoneNumber is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // NetGSM test call (SMS ile)
            Map<String, Object> netGsmResult = netGsmCallService.testCall(phoneNumber);

            Map<String, Object> response = new HashMap<>();
            response.put("testType", "NetGSM Test (SMS)");
            response.put("phoneNumber", phoneNumber);
            response.put("netGsmResult", netGsmResult);
            response.put("timestamp", System.currentTimeMillis());

            if ((Boolean) netGsmResult.get("success")) {
                response.put("success", true);
                response.put("message", "NetGSM test SMS sent successfully");
            } else {
                response.put("success", false);
                response.put("message", "NetGSM test SMS failed");
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("NetGSM test call sırasında hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * NetGSM Test SMS API - Direkt SMS testi
     * POST /api/v1/netgsm/test-sms
     */
    @PostMapping("/netgsm/test-sms")
    public ResponseEntity<Map<String, Object>> testNetGsmSms(@RequestBody Map<String, Object> testData) {
        log.info("NetGSM test SMS isteği: {}", testData);

        try {
            String phoneNumber = (String) testData.get("phoneNumber");
            String message = (String) testData.getOrDefault("message", "AI Call Center Test SMS");

            if (phoneNumber == null || phoneNumber.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("error", "phoneNumber is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // NetGSM SMS test
            Map<String, Object> smsResult = netGsmCallService.sendSms(phoneNumber, message, "TEST_SMS");

            Map<String, Object> response = new HashMap<>();
            response.put("testType", "NetGSM SMS");
            response.put("phoneNumber", phoneNumber);
            response.put("message", message);
            response.put("smsResult", smsResult);
            response.put("timestamp", System.currentTimeMillis());

            if ((Boolean) smsResult.get("success")) {
                response.put("success", true);
                response.put("message", "NetGSM SMS sent successfully");
            } else {
                response.put("success", false);
                response.put("message", "NetGSM SMS failed");
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("NetGSM test SMS sırasında hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * NetGSM SIP Test Call API - Gerçek SIP arama testi
     * POST /api/v1/netgsm/test-sip-call
     */
    @PostMapping("/netgsm/test-sip-call")
    public ResponseEntity<Map<String, Object>> testNetGsmSipCall(@RequestBody Map<String, Object> testData) {
        log.info("NetGSM SIP test call isteği: {}", testData);

        try {
            String phoneNumber = (String) testData.get("phoneNumber");
            String message = (String) testData.getOrDefault("message", "AI Call Center SIP Test - Gerçek arama testi");

            if (phoneNumber == null || phoneNumber.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("error", "phoneNumber is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // NetGSM SIP test call
            Map<String, Object> sipResult = netGsmCallService.makeSipCall(phoneNumber, message, "TEST_SIP_CAMPAIGN");

            Map<String, Object> response = new HashMap<>();
            response.put("testType", "NetGSM SIP Call");
            response.put("phoneNumber", phoneNumber);
            response.put("message", message);
            response.put("sipResult", sipResult);
            response.put("timestamp", System.currentTimeMillis());

            if ((Boolean) sipResult.get("success")) {
                response.put("success", true);
                response.put("message", "NetGSM SIP call initiated successfully");
            } else {
                response.put("success", false);
                response.put("message", "NetGSM SIP call failed");
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("NetGSM SIP test call sırasında hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * SIP Stack Status Check API
     * GET /api/v1/sip/status
     */
    @GetMapping("/sip/status")
    public ResponseEntity<Map<String, Object>> getSipStatus() {
        log.info("SIP Stack status kontrolü");

        Map<String, Object> response = new HashMap<>();
        response.put("sipStackInitialized", true);
        response.put("localPort", 5061);
        response.put("protocol", "UDP");
        response.put("netgsmGateway", "sip.netgsm.com.tr:5060");
        response.put("authUser", "2129091697");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * Network Connectivity Test API
     * POST /api/v1/sip/test-connectivity
     */
    @PostMapping("/sip/test-connectivity")
    public ResponseEntity<Map<String, Object>> testConnectivity(@RequestBody Map<String, Object> testData) {
        log.info("Network connectivity test");

        try {
            String target = (String) testData.getOrDefault("target", "sip.netgsm.com.tr");
            int port = (Integer) testData.getOrDefault("port", 5060);

            Map<String, Object> response = new HashMap<>();
            response.put("target", target);
            response.put("port", port);
            response.put("timestamp", System.currentTimeMillis());

            // DNS resolution test
            try {
                java.net.InetAddress address = java.net.InetAddress.getByName(target);
                response.put("dnsResolution", "SUCCESS");
                response.put("resolvedIP", address.getHostAddress());
            } catch (Exception e) {
                response.put("dnsResolution", "FAILED");
                response.put("dnsError", e.getMessage());
            }

            // Socket connectivity test
            try {
                java.net.Socket socket = new java.net.Socket();
                socket.connect(new java.net.InetSocketAddress(target, port), 5000);
                socket.close();
                response.put("socketConnection", "SUCCESS");
            } catch (Exception e) {
                response.put("socketConnection", "FAILED");
                response.put("socketError", e.getMessage());
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Connectivity test sırasında hata", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Twilio Test Call API
     * POST /api/v1/twilio/test-call
     */
    @PostMapping("/twilio/test-call")
    public ResponseEntity<Map<String, Object>> testTwilioCall(@RequestBody Map<String, Object> testData) {
        log.info("Twilio test call isteği: {}", testData);

        try {
            String phoneNumber = (String) testData.get("phoneNumber");
            String message = (String) testData.getOrDefault("message", "BMW AI Call Center Twilio Test - Sistem çalışıyor!");

            if (phoneNumber == null || phoneNumber.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("error", "phoneNumber is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // Twilio test call
            Map<String, Object> twilioResult = twilioCallService.makeCall(phoneNumber, message, "TWILIO_TEST_CAMPAIGN");

            Map<String, Object> response = new HashMap<>();
            response.put("testType", "Twilio Voice Call");
            response.put("phoneNumber", phoneNumber);
            response.put("message", message);
            response.put("twilioResult", twilioResult);
            response.put("timestamp", System.currentTimeMillis());

            if ((Boolean) twilioResult.get("success")) {
                response.put("success", true);
                response.put("message", "Twilio call initiated successfully");
            } else {
                response.put("success", false);
                response.put("message", "Twilio call failed");
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Twilio test call sırasında hata oluştu", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Twilio Call Status API
     * GET /api/v1/twilio/call-status/{callSid}
     */
    @GetMapping("/twilio/call-status/{callSid}")
    public ResponseEntity<Map<String, Object>> getTwilioCallStatus(@PathVariable String callSid) {
        log.info("Twilio call status sorgusu: {}", callSid);

        try {
            Map<String, Object> statusResult = twilioCallService.getCallStatus(callSid);

            Map<String, Object> response = new HashMap<>();
            response.put("callSid", callSid);
            response.put("statusResult", statusResult);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Twilio call status sorgulanırken hata", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}

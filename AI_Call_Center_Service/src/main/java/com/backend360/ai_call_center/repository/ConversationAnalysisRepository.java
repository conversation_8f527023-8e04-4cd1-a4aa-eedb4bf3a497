package com.backend360.ai_call_center.repository;

import com.backend360.ai_call_center.entity.ConversationAnalysis;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ConversationAnalysisRepository extends JpaRepository<ConversationAnalysis, Long> {

    Optional<ConversationAnalysis> findByCallId(String callId);

    List<ConversationAnalysis> findByPhoneNumberOrderByCreatedAtDesc(String phoneNumber);

    List<ConversationAnalysis> findByCustomerNameOrderByCreatedAtDesc(String customerName);

    // Campaign Analysis
    List<ConversationAnalysis> findByOriginalCampaignNameOrderByCreatedAtDesc(String campaignName);

    List<ConversationAnalysis> findByOriginalBrandNameOrderByCreatedAtDesc(String brandName);

    List<ConversationAnalysis> findByOriginalCampaignCategoryOrderByCreatedAtDesc(String category);

    // Interest Level Queries
    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.originalCampaignInterest = :interestLevel ORDER BY ca.createdAt DESC")
    List<ConversationAnalysis> findByInterestLevel(@Param("interestLevel") String interestLevel);

    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.estimatedConversionProbability >= :minProbability ORDER BY ca.estimatedConversionProbability DESC")
    List<ConversationAnalysis> findHighConversionProbability(@Param("minProbability") Double minProbability);

    // Follow-up Queries
    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.followUpPriority = 'HIGH' AND ca.nextBestAction != 'NO_ACTION' ORDER BY ca.createdAt DESC")
    List<ConversationAnalysis> findHighPriorityFollowUps();

    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.scheduledCallback = true ORDER BY ca.createdAt DESC")
    List<ConversationAnalysis> findScheduledCallbacks();

    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.nextBestAction = :action ORDER BY ca.createdAt DESC")
    List<ConversationAnalysis> findByNextBestAction(@Param("action") String action);

    // Customer Segmentation
    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.customerIncomeLevel = :incomeLevel ORDER BY ca.createdAt DESC")
    List<ConversationAnalysis> findByIncomeLevel(@Param("incomeLevel") String incomeLevel);

    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.customerMood = :mood ORDER BY ca.createdAt DESC")
    List<ConversationAnalysis> findByCustomerMood(@Param("mood") String mood);

    // Multi-brand Interest
    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.interestedOtherBrands IS NOT NULL AND ca.interestedOtherBrands != '' ORDER BY ca.createdAt DESC")
    List<ConversationAnalysis> findMultiBrandInterests();

    // Statistics Queries
    @Query("SELECT COUNT(ca) FROM ConversationAnalysis ca WHERE ca.originalCampaignInterest = 'HIGH'")
    Long countHighInterestCalls();

    @Query("SELECT COUNT(ca) FROM ConversationAnalysis ca WHERE ca.wantsTestDrive = true")
    Long countTestDriveRequests();

    @Query("SELECT COUNT(ca) FROM ConversationAnalysis ca WHERE ca.scheduledCallback = true")
    Long countScheduledCallbacks();

    @Query("SELECT AVG(ca.conversationDurationSeconds) FROM ConversationAnalysis ca WHERE ca.conversationDurationSeconds > 0")
    Double getAverageConversationDuration();

    @Query("SELECT AVG(ca.estimatedConversionProbability) FROM ConversationAnalysis ca WHERE ca.estimatedConversionProbability IS NOT NULL")
    Double getAverageConversionProbability();

    // Brand Interest Analysis
    @Query("SELECT ca.originalBrandName, COUNT(ca), AVG(ca.estimatedConversionProbability) FROM ConversationAnalysis ca GROUP BY ca.originalBrandName ORDER BY COUNT(ca) DESC")
    List<Object[]> getBrandPerformanceStats();

    @Query("SELECT ca.originalCampaignCategory, COUNT(ca), AVG(ca.estimatedConversionProbability) FROM ConversationAnalysis ca GROUP BY ca.originalCampaignCategory ORDER BY COUNT(ca) DESC")
    List<Object[]> getCategoryPerformanceStats();

    // Time-based Analysis
    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.createdAt BETWEEN :startDate AND :endDate ORDER BY ca.createdAt DESC")
    List<ConversationAnalysis> findByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Query("SELECT EXTRACT(HOUR FROM ca.createdAt) as hour, COUNT(ca) as count, AVG(ca.estimatedConversionProbability) as avgConversion FROM ConversationAnalysis ca WHERE CAST(ca.createdAt AS DATE) = CURRENT_DATE GROUP BY EXTRACT(HOUR FROM ca.createdAt) ORDER BY hour")
    List<Object[]> getHourlyConversationStats();

    // Customer Journey Analysis
    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.phoneNumber = :phoneNumber ORDER BY ca.createdAt ASC")
    List<ConversationAnalysis> findCustomerJourney(@Param("phoneNumber") String phoneNumber);

    // Recent High-Value Prospects
    @Query("SELECT ca FROM ConversationAnalysis ca WHERE ca.estimatedConversionProbability >= 0.7 AND ca.createdAt >= :since ORDER BY ca.estimatedConversionProbability DESC")
    List<ConversationAnalysis> findRecentHighValueProspects(@Param("since") LocalDateTime since);

    // Sentiment Analysis
    @Query("SELECT ca.conversationSentiment, COUNT(ca) FROM ConversationAnalysis ca GROUP BY ca.conversationSentiment")
    List<Object[]> getSentimentDistribution();

    @Query("SELECT ca.customerMood, COUNT(ca) FROM ConversationAnalysis ca GROUP BY ca.customerMood")
    List<Object[]> getCustomerMoodDistribution();
}

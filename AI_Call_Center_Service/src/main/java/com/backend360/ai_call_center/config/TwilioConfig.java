package com.backend360.ai_call_center.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "twilio")
@Data
public class TwilioConfig {
    
    // Twilio Account bilgileri
    private String accountSid = "your-account-sid";
    private String authToken = "your-auth-token";
    private String phoneNumber = "+**********"; // Twilio phone number
    
    // Voice call settings
    private String voiceUrl = "http://demo.twilio.com/docs/voice.xml"; // TwiML URL
    private String statusCallback = ""; // Webhook URL for call status
    private int timeout = 30; // Ring timeout in seconds
    private boolean record = false; // Record calls
    
    // Regional settings - Default US region kullanacak
    // private String region = "dublin"; // EU region for GDPR compliance
    // private String edge = "dublin";
    
    public boolean isConfigured() {
        return accountSid != null && !accountSid.equals("your-account-sid") &&
               authToken != null && !authToken.equals("your-auth-token") &&
               phoneNumber != null && !phoneNumber.equals("+**********");
    }
    
    public String getFromNumber() {
        return phoneNumber;
    }
}

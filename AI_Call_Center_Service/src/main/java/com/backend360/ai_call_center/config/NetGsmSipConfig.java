package com.backend360.ai_call_center.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "netgsm.sip")
@Data
public class NetGsmSipConfig {
    
    private String phoneNumber = "2129091697";
    private String gateway = "sip.netgsm.com.tr";
    private String secondaryGateway = "";
    private String authenticationUser = "2129091697";
    private String authenticationPass = "HJHjgremm8s9";
    private int port = 5060; // Default SIP port
    private String protocol = "UDP"; // SIP protocol
    private boolean enabled = true;
    
    // Connection settings
    private int connectionTimeout = 30000; // 30 seconds
    private int readTimeout = 60000; // 60 seconds
    private int maxRetries = 3;
    
    // Call settings
    private int maxCallDuration = 300; // 5 minutes max call duration
    private String defaultCodec = "G711"; // Audio codec
    private boolean recordCalls = true;
    
    public String getSipUri() {
        return String.format("sip:%s@%s:%d", authenticationUser, gateway, port);
    }
    
    public String getFromHeader() {
        return String.format("sip:%s@%s", phoneNumber, gateway);
    }
    
    public String getContactHeader() {
        return String.format("<sip:%s@%s:%d>", authenticationUser, gateway, port);
    }
    
    public boolean isConfigured() {
        return phoneNumber != null && !phoneNumber.isEmpty() &&
               gateway != null && !gateway.isEmpty() &&
               authenticationUser != null && !authenticationUser.isEmpty() &&
               authenticationPass != null && !authenticationPass.isEmpty();
    }
}

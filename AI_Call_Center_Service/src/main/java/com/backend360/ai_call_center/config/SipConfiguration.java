package com.backend360.ai_call_center.config;

import com.backend360.ai_call_center.service.SipClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class SipConfiguration {

    private final SipClientService sipClientService;

    /**
     * Uygulama başladığında SIP Stack'i başlatır
     */
    @Bean
    public CommandLineRunner initializeSipStack() {
        return args -> {
            try {
                log.info("SIP Stack başlatılıyor...");
                sipClientService.initializeSipStack();
                log.info("SIP Stack başarıyla başlatıldı!");
            } catch (Exception e) {
                log.error("SIP Stack başlat<PERSON><PERSON><PERSON><PERSON><PERSON> hata olu<PERSON>: {}", e.getMessage());
                // SIP hatası uygulam<PERSON> durdur<PERSON>n, sadece log'la
            }
        };
    }
}

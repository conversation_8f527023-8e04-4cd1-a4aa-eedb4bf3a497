spring:
  application:
    name: ai-call-center-service
  
  # H2 Database for testing
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect

server:
  port: 8085

# Disable cloud config for local testing
spring.cloud.config.enabled: false
eureka.client.enabled: false

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.backend360.ai_call_center: DEBUG
    org.springframework.web: INFO
    org.springframework.data: DEBUG

# NetGSM Configuration
netgsm:
  username: ${NETGSM_USERNAME:your-netgsm-username}
  password: ${NETGSM_PASSWORD:your-netgsm-password}
  voice:
    api:
      url: https://api.netgsm.com.tr/voice
  caller:
    number: ${NETGSM_CALLER_NUMBER:+************}

  # NetGSM SIP Trunk Configuration (Korunuyor)
  sip:
    phone-number: "**********"
    gateway: "sip.netgsm.com.tr"
    secondary-gateway: ""
    authentication-user: "**********"
    authentication-pass: "HJHjgremm8s9"
    port: 5060
    protocol: "UDP"
    enabled: true
    connection-timeout: 30000
    read-timeout: 60000
    max-retries: 3
    max-call-duration: 300
    default-codec: "G711"
    record-calls: true

# Twilio Configuration - GERÇEK CREDENTIALS
twilio:
  account-sid: "**********************************"
  auth-token: "0597464778aa90d4a99d77ebf598acbf"
  phone-number: "+***********"  # Gerçek Twilio phone number
  voice-url: "https://many-experts-play.loca.lt/api/v1/twiml/voice"
  status-callback: "https://many-experts-play.loca.lt/api/v1/twiml/status"
  timeout: 30
  record: false
  # region: "dublin"  # Default US region kullanacak
  # edge: "dublin"

# AI Configuration - Gerçek API key'ler
ai:
  # AI Provider (openai, gemini, claude)
  provider: gemini

  # Google Gemini Configuration
  gemini:
    api:
      key: "AIzaSyAfLW_fKFKs9xHchmbj7e22SqbFJpVJt-A"  # Gemini API key

  # Text-to-Speech Configuration
  tts:
    provider: google
    google:
      api:
        key: "AIzaSyDCk-wH9dNd4-cHZg4HvRm0jL9qs5QkkaA"  # Google Cloud API key
      voice:
        name: "tr-TR-Wavenet-A"  # En kaliteli Türkçe kadın sesi
        language_code: "tr-TR"
        ssml_gender: "FEMALE"
        speaking_rate: 1.5  # Konuşma hızı (1.5x hızlı)
        pitch: 0.0  # Normal ses tonu
      audio:
        encoding: "MP3"
        sample_rate_hertz: 24000  # Yüksek kalite

  # Speech-to-Text Configuration
  stt:
    provider: google
    google:
      api:
        key: "AIzaSyDCk-wH9dNd4-cHZg4HvRm0jL9qs5QkkaA"  # Google Cloud API key

# ElevenLabs Configuration for High-Quality Turkish Voice
elevenlabs:
  api:
    key: "***************************************************"  # Gerçek ElevenLabs API key
    base-url: "https://api.elevenlabs.io/v1"
  voice:
    id: "pNInz6obpgDQGcFmaJgB"  # Adam - Premium Turkish voice
    name: "Adam"
    language: "tr"
    stability: 0.5
    similarity_boost: 0.8
    style: 0.0
    use_speaker_boost: true
  audio:
    format: "mp3_44100_128"  # High quality MP3
    optimize_streaming_latency: 2

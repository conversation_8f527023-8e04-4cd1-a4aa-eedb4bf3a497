package com.crm.api.ai_call_center.service;

import com.backend360.ai_call_center.service.SmartCampaignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CallScriptService {

    private final SmartCampaignService smartCampaignService;

    /**
     * Kampanya kategorisine göre AI konuşma script'i oluşturur
     */
    public String generateCallScript(String campaignName, String brandName, String customerName, String campaignCategory) {
        log.info("Call script oluşturuluyor - Kategori: {}, Marka: {}, Kampanya: {}", campaignCategory, brandName, campaignName);
        
        String baseGreeting = String.format(
            "Merhaba, ben Sinem. Sizi 360 Avantajlı'dan arıyorum. %s ile mi görüşüyorum? " +
            "Size %s %s kampanyası hakkında bilgi vermek için arıyorum.",
            customerName, brandName, campaignName
        );
        
        if (isAutomotiveCategory(campaignCategory)) {
            return generateAutomotiveScript(baseGreeting, brandName, campaignName);
        } else {
            return generateGeneralScript(baseGreeting, brandName, campaignName);
        }
    }

    /**
     * Otomotiv/Motosiklet kategorisi için özel script
     */
    private String generateAutomotiveScript(String baseGreeting, String brandName, String campaignName) {
        return baseGreeting + " " +
            "Bu kampanya kapsamında size özel fırsatlar sunuyoruz. " +
            "Kampanya detaylarını kısaca açıklayayım: " + campaignName + ". " +
            "Bu fırsattan yararlanmak ister misiniz? " +
            "[MÜŞTERI CEVABI BEKLENİYOR] " +
            "Harika! Ayrıca size ücretsiz test sürüşü imkanı da sunabiliyoruz. " +
            "Test sürüşü yapmak ister misiniz? " +
            "[TEST SÜRÜŞÜ CEVABI BEKLENİYOR] " +
            "Mükemmel! Son olarak, " + brandName + " marka yetkilisinden " +
            "daha detaylı bilgi almanız için sizi arayabilir mi? " +
            "[DETAYLI BİLGİ CEVABI BEKLENİYOR] " +
            "Teşekkür ederim, iyi günler dilerim.";
    }

    /**
     * Genel kategoriler için script
     */
    private String generateGeneralScript(String baseGreeting, String brandName, String campaignName) {
        return baseGreeting + " " +
            "Bu kampanya kapsamında size özel avantajlar sunuyoruz. " +
            "Kampanya hakkında kısaca bilgi vereyim: " + campaignName + ". " +
            "Bu fırsattan yararlanmak ister misiniz? " +
            "[MÜŞTERI CEVABI BEKLENİYOR] " +
            "Harika! " + brandName + " marka yetkilisinden " +
            "daha detaylı bilgi almanız için sizi arayabilir mi? " +
            "[DETAYLI BİLGİ CEVABI BEKLENİYOR] " +
            "Teşekkür ederim, iyi günler dilerim.";
    }

    /**
     * Müşteri cevaplarına göre AI'nın vereceği yanıtlar (Campaign Service entegreli)
     */
    public String generateResponseToCustomer(String customerResponse, String conversationContext) {
        log.info("Müşteri cevabı analiz ediliyor: {}", customerResponse);

        String lowerResponse = customerResponse.toLowerCase();

        // Olumlu cevaplar
        if (containsPositiveWords(lowerResponse)) {
            if (conversationContext.contains("TEST_SÜRÜŞÜ")) {
                return "Mükemmel! Test sürüşü için size en yakın bayimizden iletişime geçeceğiz. " +
                       "Hangi gün ve saatte müsait olduğunuzu belirtir misiniz?";
            } else if (conversationContext.contains("DETAYLI_BİLGİ")) {
                return "Harika! Marka yetkilimiz en kısa sürede sizi arayacak. " +
                       "Size en uygun saat dilimi nedir?";
            } else {
                // Dinamik kampanya önerisi ekle
                String campaignSuggestion = smartCampaignService.generateCampaignSuggestionScript(
                    customerResponse, "", "INTERESTED_CUSTOMER", conversationContext);
                return "Çok güzel! " + campaignSuggestion;
            }
        }

        // Olumsuz cevaplar - alternatif kampanya öner
        if (containsNegativeWords(lowerResponse)) {
            String alternativeSuggestion = smartCampaignService.generateCampaignSuggestionScript(
                customerResponse, "", "SKEPTICAL_CUSTOMER", conversationContext);
            return "Anlıyorum. " + alternativeSuggestion + " İyi günler dilerim.";
        }

        // Belirsiz cevaplar - genel kampanya bilgisi
        String generalSuggestion = smartCampaignService.generateCampaignSuggestionScript(
            customerResponse, "", "NEUTRAL_CUSTOMER", conversationContext);
        return "Anlıyorum. " + generalSuggestion + " İsterseniz size daha sonra tekrar ulaşabiliriz.";
    }

    /**
     * Konuşma sonucu analizi
     */
    public CallResult analyzeCallResult(String fullConversation) {
        log.info("Konuşma sonucu analiz ediliyor");
        
        String lowerConversation = fullConversation.toLowerCase();
        
        CallResult result = new CallResult();
        result.setInterestInCampaign(containsPositiveWords(lowerConversation));
        result.setWantsTestDrive(lowerConversation.contains("test") && containsPositiveWords(lowerConversation));
        result.setWantsDetailedInfo(lowerConversation.contains("detay") && containsPositiveWords(lowerConversation));
        
        // Genel sentiment analizi
        if (containsPositiveWords(lowerConversation)) {
            result.setSentiment("POSITIVE");
            result.setNextAction("FOLLOW_UP");
        } else if (containsNegativeWords(lowerConversation)) {
            result.setSentiment("NEGATIVE");
            result.setNextAction("NO_ACTION");
        } else {
            result.setSentiment("NEUTRAL");
            result.setNextAction("SCHEDULE_CALLBACK");
        }
        
        return result;
    }

    /**
     * Detaylı konuşma transcript'i oluştur
     */
    public String generateDetailedTranscript(String aiScript, CallResult callResult, String customerResponses) {
        StringBuilder transcript = new StringBuilder();
        transcript.append("=== KONUŞMA TRANSCRİPT'İ ===\n");
        transcript.append("Tarih: ").append(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm"))).append("\n");
        transcript.append("Agent: Ayşe (AI Assistant)\n");
        transcript.append("Durum: ").append(callResult.getSentiment()).append("\n");
        transcript.append("Sonraki Aksiyon: ").append(callResult.getNextAction()).append("\n\n");

        // Konuşma adımlarını parse et ve detaylandır
        String[] scriptParts = aiScript.split("\\[.*?\\]");
        String[] responses = customerResponses != null ? customerResponses.split("\\|") : new String[0];

        transcript.append("=== KONUŞMA DETAYI ===\n\n");

        int responseIndex = 0;
        for (int i = 0; i < scriptParts.length; i++) {
            String scriptPart = scriptParts[i].trim();
            if (!scriptPart.isEmpty()) {
                // AI konuşması
                transcript.append(String.format("[%02d:%02d] AYŞE: %s\n",
                    i * 30 / 60, (i * 30) % 60, scriptPart));

                // Müşteri cevabı (varsa)
                if (responseIndex < responses.length) {
                    String customerResponse = responses[responseIndex].trim();
                    if (!customerResponse.isEmpty()) {
                        transcript.append(String.format("[%02d:%02d] MÜŞTERİ: %s\n",
                            (i * 30 + 15) / 60, ((i * 30 + 15) % 60), customerResponse));
                    }
                    responseIndex++;
                }
                transcript.append("\n");
            }
        }

        // Konuşma analizi
        transcript.append("=== KONUŞMA ANALİZİ ===\n");
        transcript.append("• Kampanya İlgisi: ").append(callResult.isInterestInCampaign() ? "VAR" : "YOK").append("\n");
        transcript.append("• Test Sürüşü İsteği: ").append(callResult.isWantsTestDrive() ? "EVET" : "HAYIR").append("\n");
        transcript.append("• Detaylı Bilgi İsteği: ").append(callResult.isWantsDetailedInfo() ? "EVET" : "HAYIR").append("\n");
        transcript.append("• Genel Sentiment: ").append(callResult.getSentiment()).append("\n");
        transcript.append("• Önerilen Aksiyon: ").append(getActionDescription(callResult.getNextAction())).append("\n\n");

        // Özet
        transcript.append("=== ÖZET ===\n");
        transcript.append(generateConversationSummary(callResult));

        return transcript.toString();
    }

    /**
     * Konuşma özeti oluştur
     */
    private String generateConversationSummary(CallResult callResult) {
        StringBuilder summary = new StringBuilder();

        if (callResult.isInterestInCampaign()) {
            summary.append("✅ Müşteri kampanyaya ilgi gösterdi. ");

            if (callResult.isWantsTestDrive()) {
                summary.append("Test sürüşü talep etti. ");
            }

            if (callResult.isWantsDetailedInfo()) {
                summary.append("Detaylı bilgi almak istiyor. ");
            }

            summary.append("Takip edilmesi öneriliyor.");
        } else {
            summary.append("❌ Müşteri kampanyaya ilgi göstermedi. ");

            switch (callResult.getSentiment()) {
                case "NEGATIVE":
                    summary.append("Olumsuz tepki verdi. Şu an için takip önerilmiyor.");
                    break;
                case "NEUTRAL":
                    summary.append("Kararsız kaldı. Daha sonra tekrar aranabilir.");
                    break;
                default:
                    summary.append("Genel olarak nötr bir yaklaşım sergiledi.");
            }
        }

        return summary.toString();
    }

    /**
     * Aksiyon açıklaması
     */
    private String getActionDescription(String action) {
        switch (action) {
            case "FOLLOW_UP":
                return "Müşteriyi takip et, detaylı bilgi ver";
            case "SCHEDULE_CALLBACK":
                return "Daha sonra tekrar ara";
            case "NO_ACTION":
                return "Herhangi bir aksiyon gerekmiyor";
            default:
                return "Belirsiz";
        }
    }

    private boolean isAutomotiveCategory(String category) {
        if (category == null) return false;
        String lowerCategory = category.toLowerCase();
        return lowerCategory.contains("otomotiv") || 
               lowerCategory.contains("motosiklet") || 
               lowerCategory.contains("araç") ||
               lowerCategory.contains("motor");
    }

    private boolean containsPositiveWords(String text) {
        String[] positiveWords = {
            "evet", "tamam", "olur", "istiyorum", "isterim", "güzel", "harika", 
            "mükemmel", "kabul", "onaylıyorum", "ilgiliyim", "beğendim"
        };
        
        for (String word : positiveWords) {
            if (text.contains(word)) {
                return true;
            }
        }
        return false;
    }

    private boolean containsNegativeWords(String text) {
        String[] negativeWords = {
            "hayır", "istemiyorum", "istemem", "olmaz", "gerek yok", 
            "ilgim yok", "rahatsız etmeyin", "aramayın"
        };
        
        for (String word : negativeWords) {
            if (text.contains(word)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Konuşma sonucu data class
     */
    public static class CallResult {
        private boolean interestInCampaign;
        private boolean wantsTestDrive;
        private boolean wantsDetailedInfo;
        private String sentiment;
        private String nextAction;
        
        // Getters and Setters
        public boolean isInterestInCampaign() { return interestInCampaign; }
        public void setInterestInCampaign(boolean interestInCampaign) { this.interestInCampaign = interestInCampaign; }
        
        public boolean isWantsTestDrive() { return wantsTestDrive; }
        public void setWantsTestDrive(boolean wantsTestDrive) { this.wantsTestDrive = wantsTestDrive; }
        
        public boolean isWantsDetailedInfo() { return wantsDetailedInfo; }
        public void setWantsDetailedInfo(boolean wantsDetailedInfo) { this.wantsDetailedInfo = wantsDetailedInfo; }
        
        public String getSentiment() { return sentiment; }
        public void setSentiment(String sentiment) { this.sentiment = sentiment; }
        
        public String getNextAction() { return nextAction; }
        public void setNextAction(String nextAction) { this.nextAction = nextAction; }
    }
}

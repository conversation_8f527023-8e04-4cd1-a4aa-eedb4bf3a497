package com.crm.api.ai_call_center.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class SpeechToTextService {

    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${ai.stt.provider:google}")
    private String sttProvider;
    
    @Value("${ai.stt.google.api.key:}")
    private String googleApiKey;
    
    @Value("${ai.stt.azure.api.key:}")
    private String azureApiKey;
    
    @Value("${ai.stt.openai.api.key:}")
    private String openaiApiKey;

    /**
     * Sesi metne çevirir - Provider'a göre
     */
    public String convertSpeechToText(String base64Audio, String audioFormat) {
        log.info("Speech-to-Text dönüşümü başlatılıyor - Provider: {}, Format: {}", sttProvider, audioFormat);
        
        switch (sttProvider.toLowerCase()) {
            case "google":
                return convertWithGoogle(base64Audio, audioFormat);
            case "azure":
                return convertWithAzure(base64Audio, audioFormat);
            case "openai":
            case "whisper":
                return convertWithOpenAI(base64Audio, audioFormat);
            default:
                log.warn("Bilinmeyen STT provider: {}, Google kullanılıyor", sttProvider);
                return convertWithGoogle(base64Audio, audioFormat);
        }
    }

    /**
     * Google Cloud Speech-to-Text
     */
    private String convertWithGoogle(String base64Audio, String audioFormat) {
        try {
            String url = "https://speech.googleapis.com/v1/speech:recognize?key=" + googleApiKey;
            
            Map<String, Object> requestBody = new HashMap<>();
            
            // Audio configuration
            Map<String, Object> config = new HashMap<>();
            config.put("encoding", getGoogleEncoding(audioFormat));
            config.put("sampleRateHertz", 16000);
            config.put("languageCode", "tr-TR");
            config.put("enableAutomaticPunctuation", true);
            config.put("model", "phone_call");
            requestBody.put("config", config);
            
            // Audio content
            Map<String, String> audio = new HashMap<>();
            audio.put("content", base64Audio);
            requestBody.put("audio", audio);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody.containsKey("results")) {
                    java.util.List<Map<String, Object>> results = (java.util.List<Map<String, Object>>) responseBody.get("results");
                    if (!results.isEmpty()) {
                        Map<String, Object> result = results.get(0);
                        java.util.List<Map<String, Object>> alternatives = (java.util.List<Map<String, Object>>) result.get("alternatives");
                        if (!alternatives.isEmpty()) {
                            String transcript = (String) alternatives.get(0).get("transcript");
                            log.info("Google STT başarılı: {}", transcript);
                            return transcript;
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Google STT hatası", e);
        }
        
        return null;
    }

    /**
     * Azure Cognitive Services Speech-to-Text
     */
    private String convertWithAzure(String base64Audio, String audioFormat) {
        try {
            String url = "https://eastus.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1" +
                        "?language=tr-TR&format=detailed";
            
            byte[] audioBytes = java.util.Base64.getDecoder().decode(base64Audio);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.valueOf("audio/" + audioFormat.toLowerCase()));
            headers.set("Ocp-Apim-Subscription-Key", azureApiKey);
            
            HttpEntity<byte[]> entity = new HttpEntity<>(audioBytes, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if ("Success".equals(responseBody.get("RecognitionStatus"))) {
                    String transcript = (String) responseBody.get("DisplayText");
                    log.info("Azure STT başarılı: {}", transcript);
                    return transcript;
                }
            }
            
        } catch (Exception e) {
            log.error("Azure STT hatası", e);
        }
        
        return null;
    }

    /**
     * OpenAI Whisper API
     */
    private String convertWithOpenAI(String base64Audio, String audioFormat) {
        try {
            String url = "https://api.openai.com/v1/audio/transcriptions";
            
            // Multipart form data için özel implementation gerekli
            // Şimdilik basit bir yaklaşım
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "whisper-1");
            requestBody.put("language", "tr");
            requestBody.put("response_format", "text");
            
            // Base64'ü decode edip temporary file olarak gönder
            byte[] audioBytes = java.util.Base64.getDecoder().decode(base64Audio);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setBearerAuth(openaiApiKey);
            
            // Bu kısım multipart implementation gerektirir
            // Şimdilik fallback kullan
            log.warn("OpenAI Whisper multipart implementation gerekli, fallback kullanılıyor");
            
        } catch (Exception e) {
            log.error("OpenAI Whisper hatası", e);
        }
        
        return null;
    }

    /**
     * Gerçek zamanlı konuşma tanıma (streaming)
     */
    public void startStreamingRecognition(String callId, StreamingCallback callback) {
        log.info("Streaming STT başlatılıyor: {}", callId);
        
        // WebSocket veya streaming connection burada kurulacak
        // Şimdilik simulated
        new Thread(() -> {
            try {
                Thread.sleep(1000);
                callback.onTranscript(callId, "Merhaba");
                
                Thread.sleep(2000);
                callback.onTranscript(callId, "Evet, ilgiliyim");
                
                Thread.sleep(3000);
                callback.onTranscript(callId, "Test sürüşü istiyorum");
                
                callback.onComplete(callId);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                callback.onError(callId, "Streaming interrupted");
            }
        }).start();
    }

    private String getGoogleEncoding(String audioFormat) {
        switch (audioFormat.toLowerCase()) {
            case "mp3":
                return "MP3";
            case "wav":
                return "LINEAR16";
            case "flac":
                return "FLAC";
            case "ogg":
                return "OGG_OPUS";
            default:
                return "LINEAR16";
        }
    }

    /**
     * Streaming callback interface
     */
    public interface StreamingCallback {
        void onTranscript(String callId, String transcript);
        void onComplete(String callId);
        void onError(String callId, String error);
    }

    /**
     * Ses kalitesi analizi
     */
    public AudioQuality analyzeAudioQuality(String base64Audio) {
        try {
            byte[] audioBytes = java.util.Base64.getDecoder().decode(base64Audio);
            
            // Basit ses kalitesi analizi
            AudioQuality quality = new AudioQuality();
            quality.setDuration(audioBytes.length / 16000.0); // Approximate duration
            quality.setQualityScore(0.85); // Simulated quality score
            quality.setNoiseLevel("LOW");
            quality.setClarity("GOOD");
            
            return quality;
            
        } catch (Exception e) {
            log.error("Ses kalitesi analizi hatası", e);
            return null;
        }
    }

    /**
     * Audio quality data class
     */
    public static class AudioQuality {
        private double duration;
        private double qualityScore;
        private String noiseLevel;
        private String clarity;
        
        // Getters and Setters
        public double getDuration() { return duration; }
        public void setDuration(double duration) { this.duration = duration; }
        
        public double getQualityScore() { return qualityScore; }
        public void setQualityScore(double qualityScore) { this.qualityScore = qualityScore; }
        
        public String getNoiseLevel() { return noiseLevel; }
        public void setNoiseLevel(String noiseLevel) { this.noiseLevel = noiseLevel; }
        
        public String getClarity() { return clarity; }
        public void setClarity(String clarity) { this.clarity = clarity; }
    }
}

package com.crm.api.ai_call_center.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiApiService {

    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${ai.provider:openai}")
    private String aiProvider;
    
    @Value("${ai.openai.api.key:}")
    private String openaiApiKey;
    
    @Value("${ai.gemini.api.key:}")
    private String geminiApiKey;
    
    @Value("${ai.claude.api.key:}")
    private String claudeApiKey;

    /**
     * AI ile konuşma - Provider'a göre
     */
    public String generateAiResponse(String userMessage, String conversationContext, String campaignInfo) {
        log.info("AI response oluşturuluyor - Provider: {}", aiProvider);
        
        switch (aiProvider.toLowerCase()) {
            case "openai":
            case "gpt":
                return generateWithOpenAI(userMessage, conversationContext, campaignInfo);
            case "gemini":
            case "google":
                return generateWithGemini(userMessage, conversationContext, campaignInfo);
            case "claude":
                return generateWithClaude(userMessage, conversationContext, campaignInfo);
            default:
                log.warn("Bilinmeyen AI provider: {}, OpenAI kullanılıyor", aiProvider);
                return generateWithOpenAI(userMessage, conversationContext, campaignInfo);
        }
    }

    /**
     * OpenAI GPT API
     */
    private String generateWithOpenAI(String userMessage, String conversationContext, String campaignInfo) {
        try {
            String url = "https://api.openai.com/v1/chat/completions";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "gpt-4");
            requestBody.put("max_tokens", 150);
            requestBody.put("temperature", 0.7);
            
            List<Map<String, String>> messages = new ArrayList<>();
            
            // System prompt
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", createSystemPrompt(campaignInfo));
            messages.add(systemMessage);
            
            // Conversation context
            if (conversationContext != null && !conversationContext.isEmpty()) {
                Map<String, String> contextMessage = new HashMap<>();
                contextMessage.put("role", "assistant");
                contextMessage.put("content", conversationContext);
                messages.add(contextMessage);
            }
            
            // User message
            Map<String, String> userMsg = new HashMap<>();
            userMsg.put("role", "user");
            userMsg.put("content", userMessage);
            messages.add(userMsg);
            
            requestBody.put("messages", messages);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(openaiApiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
                if (!choices.isEmpty()) {
                    Map<String, Object> choice = choices.get(0);
                    Map<String, String> message = (Map<String, String>) choice.get("message");
                    String aiResponse = message.get("content");
                    log.info("OpenAI response başarılı");
                    return aiResponse;
                }
            }
            
        } catch (Exception e) {
            log.error("OpenAI API hatası", e);
        }
        
        return generateFallbackResponse(userMessage, conversationContext);
    }

    /**
     * Google Gemini API
     */
    private String generateWithGemini(String userMessage, String conversationContext, String campaignInfo) {
        try {
            String url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=" + geminiApiKey;
            
            Map<String, Object> requestBody = new HashMap<>();
            
            List<Map<String, Object>> contents = new ArrayList<>();
            Map<String, Object> content = new HashMap<>();
            
            List<Map<String, String>> parts = new ArrayList<>();
            Map<String, String> part = new HashMap<>();
            
            String fullPrompt = createSystemPrompt(campaignInfo) + "\n\n" +
                               "Konuşma Bağlamı: " + (conversationContext != null ? conversationContext : "") + "\n\n" +
                               "Müşteri: " + userMessage + "\n\n" +
                               "Sinem (AI):";
            
            part.put("text", fullPrompt);
            parts.add(part);
            content.put("parts", parts);
            contents.add(content);
            requestBody.put("contents", contents);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> candidates = (List<Map<String, Object>>) responseBody.get("candidates");
                if (!candidates.isEmpty()) {
                    Map<String, Object> candidate = candidates.get(0);
                    Map<String, Object> content1 = (Map<String, Object>) candidate.get("content");
                    List<Map<String, String>> parts1 = (List<Map<String, String>>) content1.get("parts");
                    if (!parts1.isEmpty()) {
                        String aiResponse = parts1.get(0).get("text");
                        log.info("Gemini response başarılı");
                        return aiResponse;
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Gemini API hatası", e);
        }
        
        return generateFallbackResponse(userMessage, conversationContext);
    }

    /**
     * Claude API
     */
    private String generateWithClaude(String userMessage, String conversationContext, String campaignInfo) {
        try {
            String url = "https://api.anthropic.com/v1/messages";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "claude-3-sonnet-20240229");
            requestBody.put("max_tokens", 150);
            
            List<Map<String, String>> messages = new ArrayList<>();
            
            Map<String, String> userMsg = new HashMap<>();
            userMsg.put("role", "user");
            userMsg.put("content", createSystemPrompt(campaignInfo) + "\n\n" +
                                  "Konuşma Bağlamı: " + (conversationContext != null ? conversationContext : "") + "\n\n" +
                                  "Müşteri: " + userMessage);
            messages.add(userMsg);
            
            requestBody.put("messages", messages);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("x-api-key", claudeApiKey);
            headers.set("anthropic-version", "2023-06-01");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> content = (List<Map<String, Object>>) responseBody.get("content");
                if (!content.isEmpty()) {
                    String aiResponse = (String) content.get(0).get("text");
                    log.info("Claude response başarılı");
                    return aiResponse;
                }
            }
            
        } catch (Exception e) {
            log.error("Claude API hatası", e);
        }
        
        return generateFallbackResponse(userMessage, conversationContext);
    }

    /**
     * AI için system prompt oluştur
     */
    private String createSystemPrompt(String campaignInfo) {
        return "Sen Ayşe'sin, 360 Avantajlı şirketinden profesyonel bir müşteri temsilcisisin. " +
               "Görevin: " + campaignInfo + " hakkında müşterilerle konuşmak. " +
               "\n\nKRİTİK KURALLAR:" +
               "\n- SEN BİR İNSANSIN! Asla kendini yapay zeka, robot, AI, bot olarak tanıtma" +
               "\n- Eğer sorularsa: 'Ben 360 Avantajlı müşteri temsilcisiyim' de" +
               "\n- Teknoloji hakkında sorularsa: 'Bu konuda teknik ekibimizden destek alabilirim' de" +
               "\n\nDavranış Kuralları:" +
               "\n- Samimi ve profesyonel ol" +
               "\n- Kısa ve net cevaplar ver (maksimum 2-3 cümle)" +
               "\n- Müşterinin sorularını yanıtla" +
               "\n- Kampanya avantajlarını vurgula" +
               "\n- Otomotiv kampanyalarında test sürüşü teklif et" +
               "\n- Detaylı bilgi için marka yetkilisinden aranmayı öner" +
               "\n- Türkçe konuş, saygılı ol" +
               "\n\nYASAKLI İFADELER (ASLA KULLANMA):" +
               "\n- 'Ben bir yapay zeka/AI/robot/bot'" +
               "\n- 'Yapay zeka olarak'" +
               "\n- 'Programlandım'" +
               "\n- 'Algoritma'" +
               "\n\nÖrnek cevaplar:" +
               "\n- 'Evet, bu kampanya gerçekten çok avantajlı. Size özel indirimler var.'" +
               "\n- 'Tabii ki! Test sürüşü için size en yakın bayimizden iletişime geçeceğiz.'" +
               "\n- 'Mükemmel! Marka yetkilimiz size daha detaylı bilgi verecek.'";
    }

    /**
     * AI API'si çalışmazsa fallback cevaplar
     */
    private String generateFallbackResponse(String userMessage, String conversationContext) {
        log.warn("AI API'si çalışmıyor, fallback response kullanılıyor");
        
        String lowerMessage = userMessage.toLowerCase();
        
        if (lowerMessage.contains("evet") || lowerMessage.contains("tamam") || lowerMessage.contains("olur")) {
            if (conversationContext != null && conversationContext.contains("test sürüşü")) {
                return "Harika! Test sürüşü için size en yakın bayimizden iletişime geçeceğiz. Hangi gün müsait olduğunuzu belirtir misiniz?";
            } else if (conversationContext != null && conversationContext.contains("detaylı bilgi")) {
                return "Mükemmel! Marka yetkilimiz en kısa sürede sizi arayacak. Size en uygun saat dilimi nedir?";
            } else {
                return "Çok güzel! Bu kampanyadan yararlanmanız için gerekli işlemleri başlatıyoruz.";
            }
        } else if (lowerMessage.contains("hayır") || lowerMessage.contains("istemiyorum")) {
            return "Anlıyorum, sorun değil. Belki başka bir zaman ilginizi çekebilir. İyi günler dilerim.";
        } else {
            return "Anlıyorum. Bu konuda düşünmek için zamanınız olsun. İsterseniz size daha sonra tekrar ulaşabiliriz.";
        }
    }
}

package com.crm.api.ai_call_center.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class TextToSpeechService {

    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${ai.tts.provider:google}")
    private String ttsProvider;
    
    @Value("${ai.tts.google.api.key:}")
    private String googleApiKey;
    
    @Value("${ai.tts.elevenlabs.api.key:}")
    private String elevenLabsApiKey;
    
    @Value("${ai.tts.azure.api.key:}")
    private String azureApiKey;

    @Value("${ai.tts.google.audio.background_audio.enabled:false}")
    private boolean backgroundAudioEnabled;

    @Value("${ai.tts.google.audio.background_audio.volume:0.15}")
    private double backgroundVolume;

    @Value("${ai.tts.google.audio.background_audio.type:office_ambient}")
    private String backgroundType;

    /**
     * Metni sese çevirir - Provider'a göre
     */
    public String convertTextToSpeech(String text, String voiceType) {
        log.info("Text-to-Speech dönüşümü başlatılıyor - Provider: {}, Voice: {}", ttsProvider, voiceType);

        String baseAudio;
        switch (ttsProvider.toLowerCase()) {
            case "google":
                baseAudio = convertWithGoogle(text, voiceType);
                break;
            case "elevenlabs":
                baseAudio = convertWithElevenLabs(text, voiceType);
                break;
            case "azure":
                baseAudio = convertWithAzure(text, voiceType);
                break;
            default:
                log.warn("Bilinmeyen TTS provider: {}, Google kullanılıyor", ttsProvider);
                baseAudio = convertWithGoogle(text, voiceType);
                break;
        }

        // Arka plan sesi ekle
        if (backgroundAudioEnabled && baseAudio != null) {
            return mixWithBackgroundAudio(baseAudio);
        }

        return baseAudio;
    }

    /**
     * Google Cloud Text-to-Speech
     */
    private String convertWithGoogle(String text, String voiceType) {
        try {
            String url = "https://texttospeech.googleapis.com/v1/text:synthesize?key=" + googleApiKey;
            
            Map<String, Object> requestBody = new HashMap<>();
            
            // Input text
            Map<String, String> input = new HashMap<>();
            input.put("text", text);
            requestBody.put("input", input);
            
            // Voice configuration
            Map<String, String> voice = new HashMap<>();
            voice.put("languageCode", "tr-TR");
            voice.put("name", getGoogleVoiceName(voiceType));
            voice.put("ssmlGender", "FEMALE");
            requestBody.put("voice", voice);
            
            // Audio configuration
            Map<String, String> audioConfig = new HashMap<>();
            audioConfig.put("audioEncoding", "MP3");
            audioConfig.put("speakingRate", "1.0");
            audioConfig.put("pitch", "0.0");
            requestBody.put("audioConfig", audioConfig);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getBody() != null && response.getBody().containsKey("audioContent")) {
                String audioContent = (String) response.getBody().get("audioContent");
                log.info("Google TTS başarılı");
                return audioContent; // Base64 encoded audio
            }
            
        } catch (Exception e) {
            log.error("Google TTS hatası", e);
        }
        
        return null;
    }

    /**
     * ElevenLabs Text-to-Speech (Daha kaliteli)
     */
    private String convertWithElevenLabs(String text, String voiceType) {
        try {
            String voiceId = getElevenLabsVoiceId(voiceType);
            String url = "https://api.elevenlabs.io/v1/text-to-speech/" + voiceId;
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);
            requestBody.put("model_id", "eleven_multilingual_v2");
            
            Map<String, Object> voiceSettings = new HashMap<>();
            voiceSettings.put("stability", 0.5);
            voiceSettings.put("similarity_boost", 0.75);
            voiceSettings.put("style", 0.0);
            voiceSettings.put("use_speaker_boost", true);
            requestBody.put("voice_settings", voiceSettings);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("xi-api-key", elevenLabsApiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<byte[]> response = restTemplate.postForEntity(url, entity, byte[].class);
            
            if (response.getBody() != null) {
                log.info("ElevenLabs TTS başarılı");
                return java.util.Base64.getEncoder().encodeToString(response.getBody());
            }
            
        } catch (Exception e) {
            log.error("ElevenLabs TTS hatası", e);
        }
        
        return null;
    }

    /**
     * Azure Cognitive Services Text-to-Speech
     */
    private String convertWithAzure(String text, String voiceType) {
        try {
            String url = "https://eastus.tts.speech.microsoft.com/cognitiveservices/v1";
            
            String ssml = String.format(
                "<speak version='1.0' xml:lang='tr-TR'>" +
                "<voice xml:lang='tr-TR' xml:gender='Female' name='%s'>" +
                "%s" +
                "</voice></speak>",
                getAzureVoiceName(voiceType), text
            );
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_XML);
            headers.set("Ocp-Apim-Subscription-Key", azureApiKey);
            headers.set("X-Microsoft-OutputFormat", "audio-16khz-128kbitrate-mono-mp3");
            
            HttpEntity<String> entity = new HttpEntity<>(ssml, headers);
            
            ResponseEntity<byte[]> response = restTemplate.postForEntity(url, entity, byte[].class);
            
            if (response.getBody() != null) {
                log.info("Azure TTS başarılı");
                return java.util.Base64.getEncoder().encodeToString(response.getBody());
            }
            
        } catch (Exception e) {
            log.error("Azure TTS hatası", e);
        }
        
        return null;
    }

    private String getGoogleVoiceName(String voiceType) {
        switch (voiceType.toLowerCase()) {
            case "female":
            case "sinem":
                return "tr-TR-Standard-A"; // Kadın ses
            case "male":
                return "tr-TR-Standard-B"; // Erkek ses
            default:
                return "tr-TR-Standard-A";
        }
    }

    private String getElevenLabsVoiceId(String voiceType) {
        // ElevenLabs'da önceden tanımlı Türkçe sesler
        switch (voiceType.toLowerCase()) {
            case "female":
            case "sinem":
                return "21m00Tcm4TlvDq8ikWAM"; // Rachel (female)
            case "male":
                return "29vD33N1CtxCmqQRPOHJ"; // Drew (male)
            default:
                return "21m00Tcm4TlvDq8ikWAM";
        }
    }

    private String getAzureVoiceName(String voiceType) {
        switch (voiceType.toLowerCase()) {
            case "female":
            case "sinem":
                return "tr-TR-EmelNeural"; // Türkçe kadın ses
            case "male":
                return "tr-TR-AhmetNeural"; // Türkçe erkek ses
            default:
                return "tr-TR-EmelNeural";
        }
    }

    /**
     * Arka plan sesi ile mixing
     */
    private String mixWithBackgroundAudio(String baseAudio) {
        try {
            log.info("Arka plan sesi ekleniyor - Type: {}, Volume: {}", backgroundType, backgroundVolume);

            // Base64 audio'yu decode et
            byte[] baseAudioBytes = java.util.Base64.getDecoder().decode(baseAudio);

            // Arka plan sesini al
            byte[] backgroundBytes = getBackgroundAudioBytes();

            if (backgroundBytes == null) {
                log.warn("Arka plan sesi bulunamadı, orijinal ses döndürülüyor");
                return baseAudio;
            }

            // Basit audio mixing (gerçek implementasyon için FFmpeg veya Java Sound API kullanılabilir)
            byte[] mixedAudio = simpleMixAudio(baseAudioBytes, backgroundBytes, backgroundVolume);

            // Mixed audio'yu base64'e encode et
            String result = java.util.Base64.getEncoder().encodeToString(mixedAudio);
            log.info("Arka plan sesi başarıyla eklendi");
            return result;

        } catch (Exception e) {
            log.error("Arka plan sesi eklenirken hata oluştu", e);
            return baseAudio; // Hata durumunda orijinal ses döndür
        }
    }

    /**
     * Arka plan ses dosyasını al
     */
    private byte[] getBackgroundAudioBytes() {
        try {
            String audioFile = getBackgroundAudioFile();

            // Classpath'ten ses dosyasını oku
            try (var inputStream = getClass().getResourceAsStream("/audio/background/" + audioFile)) {
                if (inputStream != null) {
                    return inputStream.readAllBytes();
                }
            }

            // Eğer dosya yoksa, simulated background noise oluştur
            return generateSimulatedBackgroundNoise();

        } catch (Exception e) {
            log.error("Arka plan ses dosyası okunamadı", e);
            return null;
        }
    }

    /**
     * Background type'a göre ses dosyası seç
     */
    private String getBackgroundAudioFile() {
        switch (backgroundType.toLowerCase()) {
            case "office_ambient":
                return "office_ambient.mp3";
            case "call_center":
                return "call_center_ambient.mp3";
            case "quiet_office":
                return "quiet_office.mp3";
            default:
                return "office_ambient.mp3";
        }
    }

    /**
     * Simulated background noise oluştur
     */
    private byte[] generateSimulatedBackgroundNoise() {
        try {
            // Basit white noise oluştur (gerçek implementasyon için daha gelişmiş algoritma kullanılabilir)
            int sampleRate = 24000;
            int duration = 3; // 3 saniye
            int numSamples = sampleRate * duration;

            byte[] noise = new byte[numSamples * 2]; // 16-bit audio
            java.util.Random random = new java.util.Random();

            for (int i = 0; i < numSamples; i++) {
                // Çok hafif noise (volume 0.05)
                short sample = (short) (random.nextGaussian() * 1000 * 0.05);
                noise[i * 2] = (byte) (sample & 0xFF);
                noise[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
            }

            log.info("Simulated background noise oluşturuldu");
            return noise;

        } catch (Exception e) {
            log.error("Simulated noise oluşturulamadı", e);
            return new byte[0];
        }
    }

    /**
     * Basit audio mixing
     */
    private byte[] simpleMixAudio(byte[] mainAudio, byte[] backgroundAudio, double backgroundVolume) {
        try {
            int mainLength = mainAudio.length;
            int bgLength = backgroundAudio.length;

            // Ana ses uzunluğunda mixed audio oluştur
            byte[] mixed = new byte[mainLength];

            for (int i = 0; i < mainLength; i += 2) {
                // 16-bit samples oku
                short mainSample = (short) ((mainAudio[i + 1] << 8) | (mainAudio[i] & 0xFF));

                // Background sample (loop if needed)
                int bgIndex = (i % bgLength);
                if (bgIndex + 1 < bgLength) {
                    short bgSample = (short) ((backgroundAudio[bgIndex + 1] << 8) | (backgroundAudio[bgIndex] & 0xFF));
                    bgSample = (short) (bgSample * backgroundVolume);

                    // Mix samples
                    int mixedSample = mainSample + bgSample;

                    // Clipping prevention
                    if (mixedSample > Short.MAX_VALUE) mixedSample = Short.MAX_VALUE;
                    if (mixedSample < Short.MIN_VALUE) mixedSample = Short.MIN_VALUE;

                    // Write back
                    mixed[i] = (byte) (mixedSample & 0xFF);
                    mixed[i + 1] = (byte) ((mixedSample >> 8) & 0xFF);
                } else {
                    // Background bittiğinde sadece main audio
                    mixed[i] = mainAudio[i];
                    mixed[i + 1] = mainAudio[i + 1];
                }
            }

            return mixed;

        } catch (Exception e) {
            log.error("Audio mixing hatası", e);
            return mainAudio; // Hata durumunda orijinal döndür
        }
    }

    /**
     * Ses dosyasını kaydet ve URL döndür
     */
    public String saveAudioAndGetUrl(String base64Audio, String callId) {
        try {
            // Burada ses dosyasını sunucuya kaydedip URL döndürebilirsiniz
            // Şimdilik simulated URL
            String audioUrl = "https://ai-call-center.com/audio/" + callId + ".mp3";
            log.info("Ses dosyası kaydedildi: {}", audioUrl);
            return audioUrl;
        } catch (Exception e) {
            log.error("Ses dosyası kaydedilirken hata", e);
            return null;
        }
    }
}

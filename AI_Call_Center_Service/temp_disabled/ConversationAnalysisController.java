package com.backend360.ai_call_center.controller;

import com.backend360.ai_call_center.entity.ConversationAnalysis;
import com.backend360.ai_call_center.entity.CustomerInterest;
import com.backend360.ai_call_center.service.ConversationAnalysisService;
import com.backend360.ai_call_center.repository.ConversationAnalysisRepository;
import com.backend360.ai_call_center.repository.CustomerInterestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/analysis")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@Slf4j
public class ConversationAnalysisController {

    private final ConversationAnalysisService conversationAnalysisService;
    private final ConversationAnalysisRepository conversationAnalysisRepository;
    private final CustomerInterestRepository customerInterestRepository;

    /**
     * Konuşma detaylı analizi
     * GET /api/v1/analysis/conversation/{callId}
     */
    @GetMapping("/conversation/{callId}")
    public ResponseEntity<ConversationAnalysis> getConversationAnalysis(@PathVariable String callId) {
        log.info("Konuşma analizi isteniyor: {}", callId);
        
        Optional<ConversationAnalysis> analysis = conversationAnalysisRepository.findByCallId(callId);
        return analysis.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    /**
     * Müşteri journey'si
     * GET /api/v1/analysis/customer/{phoneNumber}/journey
     */
    @GetMapping("/customer/{phoneNumber}/journey")
    public ResponseEntity<Map<String, Object>> getCustomerJourney(@PathVariable String phoneNumber) {
        log.info("Müşteri journey'si isteniyor: {}", phoneNumber);
        
        try {
            List<ConversationAnalysis> journey = conversationAnalysisService.getCustomerJourney(phoneNumber);
            List<CustomerInterest> interests = conversationAnalysisService.getCustomerInterests(phoneNumber);
            
            Map<String, Object> customerProfile = new HashMap<>();
            customerProfile.put("phoneNumber", phoneNumber);
            customerProfile.put("totalCalls", journey.size());
            customerProfile.put("conversationHistory", journey);
            customerProfile.put("brandInterests", interests);
            customerProfile.put("lastContact", journey.isEmpty() ? null : journey.get(journey.size() - 1).getCreatedAt());
            
            // Müşteri segmentasyonu
            if (!journey.isEmpty()) {
                ConversationAnalysis latest = journey.get(journey.size() - 1);
                customerProfile.put("customerSegment", latest.getCustomerSegment());
                customerProfile.put("conversionProbability", latest.getEstimatedConversionProbability());
                customerProfile.put("followUpPriority", latest.getFollowUpPriority());
            }
            
            return ResponseEntity.ok(customerProfile);
        } catch (Exception e) {
            log.error("Customer journey alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Marka ilgi analizi
     * GET /api/v1/analysis/brand/{brandName}/interests
     */
    @GetMapping("/brand/{brandName}/interests")
    public ResponseEntity<Map<String, Object>> getBrandInterestAnalysis(@PathVariable String brandName) {
        log.info("Marka ilgi analizi isteniyor: {}", brandName);
        
        try {
            List<CustomerInterest> brandInterests = customerInterestRepository.findByBrandNameOrderByInterestScoreDesc(brandName);
            List<CustomerInterest> hotLeads = brandInterests.stream()
                    .filter(CustomerInterest::isHotLead)
                    .toList();
            
            Map<String, Object> analysis = new HashMap<>();
            analysis.put("brandName", brandName);
            analysis.put("totalInterests", brandInterests.size());
            analysis.put("hotLeads", hotLeads.size());
            analysis.put("averageInterestScore", brandInterests.stream()
                    .mapToDouble(CustomerInterest::getInterestScore)
                    .average().orElse(0.0));
            analysis.put("testDriveRequests", brandInterests.stream()
                    .mapToLong(ci -> ci.getRequestedTestDrive() ? 1 : 0)
                    .sum());
            analysis.put("scheduledMeetings", brandInterests.stream()
                    .mapToLong(ci -> ci.getScheduledMeeting() ? 1 : 0)
                    .sum());
            analysis.put("recentInterests", brandInterests.stream().limit(10).toList());
            
            return ResponseEntity.ok(analysis);
        } catch (Exception e) {
            log.error("Brand interest analysis alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Hot leads listesi
     * GET /api/v1/analysis/leads/hot
     */
    @GetMapping("/leads/hot")
    public ResponseEntity<List<CustomerInterest>> getHotLeads() {
        log.info("Hot leads isteniyor");
        
        try {
            List<CustomerInterest> hotLeads = customerInterestRepository.findHotLeads();
            return ResponseEntity.ok(hotLeads);
        } catch (Exception e) {
            log.error("Hot leads alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Follow-up gereken müşteriler
     * GET /api/v1/analysis/followup/required
     */
    @GetMapping("/followup/required")
    public ResponseEntity<Map<String, Object>> getFollowUpRequired() {
        log.info("Follow-up gereken müşteriler isteniyor");
        
        try {
            List<ConversationAnalysis> highPriorityFollowUps = conversationAnalysisRepository.findHighPriorityFollowUps();
            List<CustomerInterest> highPriorityInterests = customerInterestRepository.findHighPriorityFollowUps();
            List<ConversationAnalysis> scheduledCallbacks = conversationAnalysisRepository.findScheduledCallbacks();
            
            Map<String, Object> followUpData = new HashMap<>();
            followUpData.put("highPriorityConversations", highPriorityFollowUps);
            followUpData.put("highPriorityInterests", highPriorityInterests);
            followUpData.put("scheduledCallbacks", scheduledCallbacks);
            followUpData.put("totalFollowUpsRequired", 
                    highPriorityFollowUps.size() + highPriorityInterests.size());
            
            return ResponseEntity.ok(followUpData);
        } catch (Exception e) {
            log.error("Follow-up data alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Marka performans karşılaştırması
     * GET /api/v1/analysis/brands/performance
     */
    @GetMapping("/brands/performance")
    public ResponseEntity<List<Object[]>> getBrandPerformanceComparison() {
        log.info("Marka performans karşılaştırması isteniyor");
        
        try {
            List<Object[]> brandStats = customerInterestRepository.getBrandInterestStats();
            return ResponseEntity.ok(brandStats);
        } catch (Exception e) {
            log.error("Brand performance comparison alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Kategori performans analizi
     * GET /api/v1/analysis/categories/performance
     */
    @GetMapping("/categories/performance")
    public ResponseEntity<List<Object[]>> getCategoryPerformance() {
        log.info("Kategori performans analizi isteniyor");
        
        try {
            List<Object[]> categoryStats = customerInterestRepository.getCategoryInterestStats();
            return ResponseEntity.ok(categoryStats);
        } catch (Exception e) {
            log.error("Category performance alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Müşteri segmentasyon analizi
     * GET /api/v1/analysis/segmentation
     */
    @GetMapping("/segmentation")
    public ResponseEntity<Map<String, Object>> getCustomerSegmentation() {
        log.info("Müşteri segmentasyon analizi isteniyor");
        
        try {
            List<Object[]> sentimentDistribution = conversationAnalysisRepository.getSentimentDistribution();
            List<Object[]> moodDistribution = conversationAnalysisRepository.getCustomerMoodDistribution();
            List<Object[]> timelineDistribution = customerInterestRepository.getTimelineDistribution();
            List<Object[]> budgetDistribution = customerInterestRepository.getBudgetDistribution();
            
            Map<String, Object> segmentation = new HashMap<>();
            segmentation.put("sentimentDistribution", sentimentDistribution);
            segmentation.put("moodDistribution", moodDistribution);
            segmentation.put("purchaseTimelineDistribution", timelineDistribution);
            segmentation.put("budgetDistribution", budgetDistribution);
            
            // High-value prospects
            LocalDateTime lastWeek = LocalDateTime.now().minusWeeks(1);
            List<ConversationAnalysis> highValueProspects = conversationAnalysisRepository
                    .findRecentHighValueProspects(lastWeek);
            segmentation.put("recentHighValueProspects", highValueProspects);
            
            return ResponseEntity.ok(segmentation);
        } catch (Exception e) {
            log.error("Customer segmentation alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Rakip analizi
     * GET /api/v1/analysis/competitors
     */
    @GetMapping("/competitors")
    public ResponseEntity<Map<String, Object>> getCompetitorAnalysis() {
        log.info("Rakip analizi isteniyor");
        
        try {
            List<CustomerInterest> competitorMentions = customerInterestRepository.findWithCompetitorMentions();
            List<CustomerInterest> comparisons = customerInterestRepository.findCompetitorComparisons();
            
            Map<String, Object> competitorAnalysis = new HashMap<>();
            competitorAnalysis.put("totalCompetitorMentions", competitorMentions.size());
            competitorAnalysis.put("totalComparisons", comparisons.size());
            competitorAnalysis.put("competitorMentions", competitorMentions.stream().limit(20).toList());
            competitorAnalysis.put("recentComparisons", comparisons.stream().limit(10).toList());
            
            return ResponseEntity.ok(competitorAnalysis);
        } catch (Exception e) {
            log.error("Competitor analysis alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Multi-brand müşteriler
     * GET /api/v1/analysis/customers/multibrand
     */
    @GetMapping("/customers/multibrand")
    public ResponseEntity<List<Object[]>> getMultiBrandCustomers() {
        log.info("Multi-brand müşteriler isteniyor");
        
        try {
            List<Object[]> multiBrandCustomers = customerInterestRepository.findMultiBrandCustomers();
            return ResponseEntity.ok(multiBrandCustomers);
        } catch (Exception e) {
            log.error("Multi-brand customers alınırken hata oluştu", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}

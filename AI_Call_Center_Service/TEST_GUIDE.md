# AI Call Center Test Rehberi

## <PERSON><PERSON><PERSON><PERSON>

### 1. TTS Provider Değ<PERSON>şikliği
- **Önceki**: Google TTS kullanılıyordu
- **Şimdi**: ElevenLabs TTS kullanılıyor (daha ka<PERSON>li Türkçe ses)
- **Konfigürasyon**: `ai.tts.provider=elevenlabs`

### 2. TwiML Voice Güncellemesi
- **Önceki**: `voice="Polly.Filiz"` (Amazon Polly)
- **Şimdi**: `voice="Google.tr-TR-Wavenet-A"` (Google TTS Türkçe)
- **Neden**: TwiML'de ElevenLabs doğrudan desteklenmiyor

### 3. AI Agent Prompt Değişikliği
- **Önceki**: BMW odaklı prompt
- **Şimdi**: CRM odaklı prompt
- **İçerik**: Kampanya formu, marka temsilcisi, 360 Avantajlı

### 4. Türkçe Dil Optimizasyonu
- STT için `language_code: "tr-TR"` eklendi
- `model: "phone_call"` telefon görüşmesi için optimize edildi
- `enable_automatic_punctuation: true` noktalama işaretleri otomatik

## Test Adımları

### 1. Servis Başlatma
```bash
cd AI_Call_Center_Service
mvn spring-boot:run -Dspring.profiles.active=local
```

### 2. TwiML Test Endpoint
```bash
curl -X GET "http://localhost:8085/api/v1/twiml/test"
```

**Beklenen Yanıt:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Google.tr-TR-Wavenet-A" language="tr-TR">
        360 Avantajlı AI Call Center TwiML test başarılı! Türkçe ses kalitesi mükemmel çalışıyor!
    </Say>
</Response>
```

### 3. Voice Call Test
```bash
curl -X POST "http://localhost:8085/api/v1/twiml/voice" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "CallSid=test123&From=+905551234567&To=+16206229044&CallStatus=in-progress"
```

**Beklenen Yanıt:**
- CRM odaklı Türkçe mesaj
- "360 Avantajlı müşteri hizmetlerinden arıyoruz"
- "Kampanya formunu doldurmuştunuz"

### 4. Speech Processing Test
```bash
curl -X POST "http://localhost:8085/api/v1/twiml/process-speech" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "SpeechResult=evet kampanya hakkında bilgi istiyorum&Confidence=0.95"
```

### 5. ElevenLabs Test
```bash
curl -X GET "http://localhost:8085/api/v1/ai-call-center/test/elevenlabs"
```

## Konfigürasyon Kontrolleri

### 1. TTS Provider Kontrolü
```bash
curl -X GET "http://localhost:8085/actuator/configprops" | grep -A 10 "tts"
```

### 2. ElevenLabs API Key Kontrolü
```bash
curl -X GET "http://localhost:8085/actuator/env" | grep -i elevenlabs
```

### 3. Türkçe Dil Ayarları Kontrolü
```bash
curl -X GET "http://localhost:8085/actuator/configprops" | grep -A 5 "language"
```

## Sorun Giderme

### 1. Hala İngilizce Ses Çıkıyorsa
- TwiML'de `voice="Google.tr-TR-Wavenet-A"` kullanıldığından emin olun
- `language="tr-TR"` parametresi eklendiğinden emin olun

### 2. ElevenLabs Çalışmıyorsa
- API key'in doğru olduğunu kontrol edin
- Voice ID'nin geçerli olduğunu kontrol edin
- Internet bağlantısını kontrol edin

### 3. STT Türkçe Tanımıyorsa
- `language_code: "tr-TR"` ayarını kontrol edin
- Google Cloud STT API key'ini kontrol edin
- Mikrofon kalitesini kontrol edin

## Önemli Notlar

1. **TwiML Sınırlaması**: TwiML'de doğrudan ElevenLabs kullanamayız, bu yüzden Google TTS kullanıyoruz
2. **ElevenLabs Entegrasyonu**: Backend'de ElevenLabs kullanılıyor, TwiML'de Google TTS
3. **Dil Ayarları**: Tüm servisler Türkçe için optimize edildi
4. **AI Prompt**: CRM odaklı, BMW referansları kaldırıldı

## Başarı Kriterleri

✅ Türkçe ses çıkması
✅ CRM odaklı konuşma
✅ "360 Avantajlı" marka adı kullanımı
✅ Kampanya formu referansları
✅ Marka temsilcisi görüşme teklifi

package com.backend360.campaign_service.service;

import com.backend360.campaign_service.crm_client.CrmClient;
import com.backend360.campaign_service.dto.CustomerToCampaignDto;
import com.backend360.campaign_service.dto.FormToCampaignDto;
import com.backend360.campaign_service.dto.crm.ContactIUDto;
import com.backend360.campaign_service.dto.crm.ContactDto;
import com.backend360.campaign_service.dto.crm.CampaignIUDto;
import com.backend360.campaign_service.dto.crm.CampaignDto;
import com.backend360.campaign_service.model.Customer;
import com.backend360.campaign_service.model.Form;
import com.backend360.campaign_service.model.Campaign;

import com.backend360.campaign_service.model.BrandToCampaign;
import com.backend360.campaign_service.repository.BrandToCampaignRepository;
import com.backend360.campaign_service.client.AiCallCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
@Slf4j
public class CrmContactIntegrationService {

    @Autowired
    private CrmClient crmClient;

    @Autowired
    private BrandToCampaignRepository brandToCampaignRepository;

    @Autowired
    private CrmTokenManager crmTokenManager;

    @Autowired
    private AiCallCenterClient aiCallCenterClient;

    public void sendCustomerToCrm(CustomerToCampaignDto customerToCampaign) {
        try {
            // Customer bilgilerini ContactIUDto'ya çevir
            ContactIUDto contact = convertCustomerToContact(customerToCampaign);

            // CRM'e gönder
            ResponseEntity<ContactDto> response = crmClient.createContact(contact, crmTokenManager.getAuthorizationHeader());

            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("Customer sent to CRM successfully: {}", customerToCampaign.getCustomer().getEmail());
            } else {
                log.error("Failed to send customer to CRM: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.warn("CRM-API bağlantısı başarısız, customer kaydedildi ama CRM'e gönderilmedi: {} - {}",
                    customerToCampaign.getCustomer().getEmail(), e.getMessage());
            // CRM hatası customer kaydını engellemez, sadece log'a yazılır
        }
    }

    public void sendFormToCrm(FormToCampaignDto formToCampaign) {
        try {
            log.info("Form-to-CRM gönderimi başlıyor: {}", formToCampaign.getForm().getEmail());

            // Form bilgilerini ContactIUDto'ya çevir
            ContactIUDto contact = convertFormToContact(formToCampaign);
            log.debug("ContactIUDto oluşturuldu: {}", contact);

            // Token bilgisini log'la
            String authHeader = crmTokenManager.getAuthorizationHeader();
            log.debug("CRM Auth Header: {}", authHeader.substring(0, 30) + "...");

            // CRM'e gönder
            log.info("CRM API'ye istek gönderiliyor...");
            ResponseEntity<ContactDto> response = crmClient.createContact(contact, authHeader);

            // CRM'e başarıyla gönderildiyse AI Call Center'ı da tetikle
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("CRM'e başarıyla gönderildi, AI Call Center tetikleniyor...");
                triggerAiCallCenter(formToCampaign);
            }

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Form başarıyla CRM'e gönderildi: {}", formToCampaign.getForm().getEmail());
            } else {
                log.error("CRM'e form gönderimi başarısız - Status: {}, Body: {}",
                        response.getStatusCode(), response.getBody());

                // 403 hatası için özel log
                if (response.getStatusCode().value() == 403) {
                    log.error("403 Forbidden hatası - Bu genellikle yetki sorunu veya yanlış endpoint anlamına gelir");
                    log.error("Contact verisi: {}", contact);
                    log.error("Auth header preview: {}", authHeader.substring(0, 30) + "...");
                }
            }

        } catch (Exception e) {
            log.warn("CRM-API bağlantısı başarısız, form kaydedildi ama CRM'e gönderilmedi: {} - {}",
                    formToCampaign.getForm().getEmail(), e.getMessage());
            log.debug("CRM hatası detayı:", e);

            // Exception tipini log'la
            log.error("Exception tipi: {}", e.getClass().getSimpleName());
            if (e.getCause() != null) {
                log.error("Cause: {}", e.getCause().getMessage());
            }

            // CRM hatası form kaydını engellemez, sadece log'a yazılır
        }
    }

    public void sendCampaignToCrm(Campaign campaign) {
        try {
            log.info("Sending campaign to CRM: {}", campaign.getName());

            // Campaign bilgilerini CampaignIUDto'ya çevir
            CampaignIUDto campaignDto = convertCampaignToCrmCampaign(campaign);

            // CRM'e gönder
            ResponseEntity<CampaignDto> response = crmClient.createCampaign(campaignDto, crmTokenManager.getAuthorizationHeader());

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Campaign sent to CRM successfully: {}", response.getBody());
            } else {
                log.error("Failed to send campaign to CRM: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Error sending campaign to CRM: {}", e.getMessage(), e);
        }
    }

    private ContactIUDto convertCustomerToContact(CustomerToCampaignDto customerToCampaign) {
        Customer customer = customerToCampaign.getCustomer();
        Campaign campaign = customerToCampaign.getCampaign();
        String brandName = getBrandNameByCampaign(campaign);

        // Campaign type'ı category'den al
        String campaignType = null;
        if (campaign != null && campaign.getCategory() != null) {
            campaignType = campaign.getCategory().getName();
        }

        return ContactIUDto.builder()
                .name(customer.getName())
                .surname(customer.getSurname())
                .email(customer.getEmail())
                .phoneNumber(customer.getPhoneNumber())
                .countryCode("+90") // Türkiye için
                .timezone("Europe/Istanbul")
                .status("Active")
                // CustomerToCampaign bilgileri
                .customerToCampaignId(customerToCampaign.getId())
                .isCalled(customerToCampaign.getIsCalled())
                .isActive(customerToCampaign.getIsActive())
                .contactPhone(customerToCampaign.getContactPhone())
                .contactEmail(customerToCampaign.getContactEmail())
                .contactName(customerToCampaign.getContactName())
                .contactSurname(customerToCampaign.getContactSurname())
                .customerToCampaignCreatedAt(customerToCampaign.getCreatedAt())
                .customerToCampaignUpdatedAt(customerToCampaign.getUpdatedAt())
                // Campaign bilgileri
                .campaignId(campaign != null ? campaign.getId() : null)
                .campaignName(campaign != null ? campaign.getName() : null)
                .campaignTitle(campaign != null ? campaign.getTitle() : null)
                .campaignDescription(campaign != null ? campaign.getDescription() : null)
                .campaignType(campaignType)
                .brandName(brandName)
                .build();
    }

    private ContactIUDto convertFormToContact(FormToCampaignDto formToCampaign) {
        Form form = formToCampaign.getForm();
        Campaign campaign = formToCampaign.getCampaign();
        String brandName = getBrandNameByCampaign(campaign);

        // Campaign type'ı category'den al
        String campaignType = null;
        if (campaign != null && campaign.getCategory() != null) {
            campaignType = campaign.getCategory().getName();
        }

        return ContactIUDto.builder()
                .name(form.getName())
                .surname(form.getSurname())
                .email(form.getEmail())
                .phoneNumber(form.getPhoneNumber())
                .countryCode("+90") // Türkiye için
                .timezone("Europe/Istanbul")
                .status("Active")
                // FormToCampaign bilgileri
                .formToCampaignId(formToCampaign.getId())
                .ipAddress(formToCampaign.getIpAddress())
                .contactPhone(formToCampaign.getContactPhone())
                .contactEmail(formToCampaign.getContactEmail())
                .contactName(formToCampaign.getContactName())
                .contactSurname(formToCampaign.getContactSurname())
                .formToCampaignCreatedAt(formToCampaign.getCreatedAt())
                .formToCampaignUpdatedAt(formToCampaign.getUpdatedAt())
                // Campaign bilgileri
                .campaignId(campaign != null ? campaign.getId() : null)
                .campaignName(campaign != null ? campaign.getName() : null)
                .campaignTitle(campaign != null ? campaign.getTitle() : null)
                .campaignDescription(campaign != null ? campaign.getDescription() : null)
                .campaignType(campaignType)
                .brandName(brandName)
                .build();
    }

    private CampaignIUDto convertCampaignToCrmCampaign(Campaign campaign) {
        Map<String, Object> rules = new HashMap<>();
        if (campaign.getDetails() != null) {
            rules.put("details", campaign.getDetails());
        }

        return CampaignIUDto.builder()
                .name(campaign.getName())
                .description(campaign.getDescription())
                .status(campaign.getIsActive() ? "active" : "inactive")
                .campaignType("web_campaign")
                .startDate(campaign.getStartDate())
                .endDate(campaign.getEndDate())
                .rules(rules)
                .agentId(1L) // Default agent ID
                .agentAppId(1L) // Default app ID
                .build();
    }

    private String getBrandNameByCampaign(Campaign campaign) {
        if (campaign == null || campaign.getId() == null) return null;
        BrandToCampaign btc = brandToCampaignRepository.findFirstByCampaign_IdAndIsActiveTrue(campaign.getId());
        if (btc != null && btc.getBrand() != null) {
            return btc.getBrand().getName();
        }
        return null;
    }

    /**
     * AI Call Center'ı tetikle
     */
    private void triggerAiCallCenter(FormToCampaignDto formToCampaign) {
        try {
            Form form = formToCampaign.getForm();
            Campaign campaign = formToCampaign.getCampaign();

            if (form.getPhoneNumber() == null || form.getPhoneNumber().trim().isEmpty()) {
                log.warn("Telefon numarası boş, AI Call Center tetiklenmiyor: {}", form.getEmail());
                return;
            }

            // AI Call Center için call data hazırla
            Map<String, Object> callData = new HashMap<>();

            // Participant bilgileri
            Map<String, Object> participant = new HashMap<>();
            participant.put("number", form.getPhoneNumber());

            // Form bilgilerini JSON string olarak about alanına koy
            Map<String, Object> aboutData = new HashMap<>();
            aboutData.put("name", form.getName());
            aboutData.put("surname", form.getSurname());
            aboutData.put("email", form.getEmail());
            aboutData.put("phoneNumber", form.getPhoneNumber());
            aboutData.put("city", form.getCity());
            aboutData.put("country", form.getCountry());
            aboutData.put("contactName", formToCampaign.getContactName());
            aboutData.put("contactSurname", formToCampaign.getContactSurname());
            aboutData.put("contactPhone", formToCampaign.getContactPhone());
            aboutData.put("contactEmail", formToCampaign.getContactEmail());

            if (campaign != null) {
                aboutData.put("campaignId", campaign.getId());
                aboutData.put("campaignName", campaign.getName());
                aboutData.put("campaignTitle", campaign.getTitle());
                aboutData.put("campaignDescription", campaign.getDescription());
                aboutData.put("brandName", getBrandNameByCampaign(campaign));
                if (campaign.getCategory() != null) {
                    aboutData.put("campaignCategory", campaign.getCategory().getName());
                }
            }

            participant.put("about", new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(aboutData));
            callData.put("participant", participant);

            // Prompt bilgileri
            Map<String, Object> prompt = new HashMap<>();
            prompt.put("content", String.format("Form dolduran müşteri: %s %s, Kampanya: %s",
                form.getName(), form.getSurname(),
                campaign != null ? campaign.getName() : "Genel"));
            callData.put("prompt", prompt);

            // Agent ID
            callData.put("agentId", "ai-agent-001");

            // AI Call Center'a gönder
            aiCallCenterClient.initiateCallWithFormData(callData);

            log.info("AI Call Center başarıyla tetiklendi: {} - {}", form.getName(), form.getPhoneNumber());

        } catch (Exception e) {
            log.error("AI Call Center tetiklenirken hata oluştu", e);
            // Hata ana işlemi etkilemesin
        }
    }
}
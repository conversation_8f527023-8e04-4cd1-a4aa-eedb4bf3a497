server:
  port: 8080

spring:
  application:
    name: ai-call-center-service

  datasource:
    url: *******************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-service:8761/eureka
    register-with-eureka: true
    fetch-registry: true
  instance:
    hostname: ai-call-center-service
    prefer-ip-address: true
    appname: AI-CALL-CENTER-SERVICE

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.backend360.ai_call_center: DEBUG
    org.springframework.web: INFO

# Campaign Service Integration
campaign:
  service:
    url: ${CAMPAIGN_SERVICE_URL:http://campaign-service:9001}

# Net GSM SIP Configuration
netgsm:
  api:
    url: https://api.netgsm.com.tr
    username: ${NETGSM_USERNAME:**********}
    password: ${NETGSM_PASSWORD:HJHjgremm8s9}
  
  # SIP Trunk Configuration
  sip:
    gateway: sip.netgsm.com.tr
    phone_number: **********
    auth_user: **********
    auth_pass: HJHjgremm8s9

# AI Configuration
ai:
  # AI Provider (openai, gemini, claude)
  provider: ${AI_PROVIDER:gemini}
  
  # OpenAI Configuration
  openai:
    api:
      key: ${OPENAI_API_KEY:your_openai_api_key}
  
  # Google Gemini Configuration  
  gemini:
    api:
      key: ${GEMINI_API_KEY:AIzaSyAfLW_fKFKs9xHchmbj7e22SqbFJpVJt-A}
  
  # Claude Configuration
  claude:
    api:
      key: ${CLAUDE_API_KEY:your_claude_api_key}
  
  # Text-to-Speech Configuration
  tts:
    provider: ${TTS_PROVIDER:elevenlabs}  # ElevenLabs daha kaliteli Türkçe ses
    google:
      api:
        key: ${GOOGLE_TTS_API_KEY:AIzaSyDCk-wH9dNd4-cHZg4HvRm0jL9qs5QkkaA}
    elevenlabs:
      api:
        key: ${ELEVENLABS_API_KEY:***************************************************}
    azure:
      api:
        key: ${AZURE_TTS_API_KEY:your_azure_tts_api_key}

  # Speech-to-Text Configuration
  stt:
    provider: ${STT_PROVIDER:google}
    google:
      api:
        key: ${GOOGLE_STT_API_KEY:AIzaSyDCk-wH9dNd4-cHZg4HvRm0jL9qs5QkkaA}
      language_code: "tr-TR"  # Türkçe dil kodu
      model: "phone_call"  # Telefon görüşmesi için optimize edilmiş model
      enable_automatic_punctuation: true
      sample_rate_hertz: 16000
    azure:
      api:
        key: ${AZURE_STT_API_KEY:your_azure_stt_api_key}
    openai:
      api:
        key: ${OPENAI_STT_API_KEY:your_openai_stt_api_key}

# ElevenLabs Configuration for High-Quality Turkish Voice
elevenlabs:
  api:
    key: ${ELEVENLABS_API_KEY:***************************************************}
    base-url: "https://api.elevenlabs.io/v1"
  voice:
    id: ${ELEVENLABS_VOICE_ID:pNInz6obpgDQGcFmaJgB}  # Adam - Premium Turkish voice
    name: "Adam"
    language: "tr"
    stability: 0.5
    similarity_boost: 0.8
    style: 0.0
    use_speaker_boost: true
  audio:
    format: "mp3_44100_128"  # High quality MP3
    optimize_streaming_latency: 2
